# OVV Batch

OVV Batch is a Spring Batch web application for processing voucher transactions across multiple countries and providers. It handles batch processing, file generation, compression, and SFTP uploads for voucher systems.

## Features

- **Multi-country support**: CZ, HU, PL, SK
- **Multiple voucher providers**: Edenred, Sodexo, TVM, LCHD, WASA, DOXX
- **Automated batch processing**: Daily and weekly job scheduling
- **File processing pipeline**: Generation, compression, and SFTP upload
- **Web-based administration**: Spring Batch Admin interface for job monitoring
- **Security**: RADIUS authentication support

## Requirements

- Java 17 or higher
- Maven 3.6+
- Oracle Database 19c (for production)
- Docker & Docker Compose (for local development)

## Quick Start

### 1. Clone the repository
```bash
git clone <repository-url>
cd new-ovv-batch-tesco
```

### 2. Set up environment variables
```bash
cp .env.example .env
# Edit .env and add your NVD API key
```

### 3. Get NVD API Key (Required for security scanning)
1. Go to https://nvd.nist.gov/developers/request-an-api-key
2. Register for a free API key
3. Check your email to activate it
4. Add the key to your environment:
   ```bash
   export NVD_API_KEY=your-api-key-here
   ```

### 4. Start local services
```bash
cd tools
docker-compose up -d  # Starts Oracle DB and FreeRADIUS
```

### 5. Build the application
```bash
mvn clean package
```

### 6. Run locally
```bash
mvn jetty:run
# Access at: http://localhost:8080/ovv-batch
```

## Development

### Common Maven Commands

```bash
# Build and package
mvn clean package              # Build the WAR file
mvn clean package -DskipTests  # Build without running tests

# Testing
mvn test                       # Run all tests
mvn test -Dtest=TestClassName  # Run specific test class

# Development server
mvn jetty:run                  # Run with embedded Jetty

# Security scanning
mvn dependency-check:check     # Run OWASP dependency check
```

### Docker Development Environment

The project includes Docker Compose configuration for local development:

```bash
cd tools

# Start all services
docker-compose up -d

# Start specific services
docker-compose up -d oracledb    # Oracle Database (port 1521)
docker-compose up -d freeradius  # FreeRADIUS (ports 1812/udp, 1813, 18120)

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Remote Debugging

The application supports remote debugging on port 5005:

```bash
# When running in Docker
docker run -p 8080:8080 -p 5005:5005 ovv-batch

# Or add to your IDE's remote debug configuration:
-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005
```

## Architecture

### Technology Stack

- **Framework**: Spring Framework 6.1.14 with Spring Batch 5.1.2
- **Database**: Oracle 19c (production), H2 (testing)
- **Security**: Spring Security 6.3.4 with RADIUS authentication
- **ORM**: Hibernate 6.6.1 with Spring Data JPA
- **Build**: Maven
- **Container**: Tomcat 10 (production), Jetty (development)

### Project Structure

```
src/
├── main/
│   ├── java/
│   │   └── cz/wincor/ovv/batch/
│   │       ├── controller/     # Web controllers
│   │       ├── service/        # Business logic
│   │       ├── repository/     # Data access layer
│   │       ├── entity/         # JPA entities
│   │       ├── tasklet/        # Batch processing tasks
│   │       ├── scheduler/      # Job schedulers
│   │       └── gateway/        # SFTP integration
│   ├── resources/
│   │   └── META-INF/spring/
│   │       └── batch/jobs/     # Batch job configurations
│   └── webapp/                 # Web resources
├── test/                       # Unit and integration tests
└── tools/
    └── docker-compose.yml      # Local development services
```

### Batch Processing Pipeline

1. **Output File Setup**: Generate output filename
2. **Batch Interval**: Set processing time window
3. **Store Loading**: Load OVV store data
4. **Data Processing**: Read and transform voucher data
5. **File Formatting**: Add headers/footers
6. **Compression**: Compress output files
7. **SFTP Upload**: Transfer files to remote servers

## Configuration

### Application Properties

Key configuration files are located in:
- `src/main/resources/application.properties`
- `src/main/resources/META-INF/spring/`

### Database Setup

Database scripts are provided in `db/sql/`:
- Schema creation scripts for Oracle, PostgreSQL, and H2
- Spring Batch schema initialization
- Migration scripts in `db/sql/update/`

## Deployment

### Build WAR file
```bash
mvn clean package
# Output: target/ovv-batch.war
```

### Docker Deployment
```bash
# Build Docker image
docker build -t ovv-batch .

# Run container
docker run -d \
  -p 8080:8080 \
  -p 5005:5005 \
  -e NVD_API_KEY=$NVD_API_KEY \
  ovv-batch
```

## Testing

The project uses JUnit 5 and Mockito for testing:

```bash
# Run all tests
mvn test

# Run with specific profile
mvn test -Dspring.profiles.active=test

# Generate test reports
mvn surefire-report:report
```

## Security

- **OWASP Dependency Check**: Automated security vulnerability scanning
- **RADIUS Authentication**: Secure user authentication
- **Spring Security**: Web application security

To run security scans:
```bash
export NVD_API_KEY=your-api-key-here
mvn dependency-check:check
```

### Recent Vulnerability Fixes

The following vulnerabilities have been addressed by updating dependencies:

- **Fixed**: CVE-2024-38827 (Spring Security 6.3.4 → 6.3.5)
- **Remaining**: CVE-2024-57699 (json-smart 2.5.1) - Latest available version still vulnerable
- **Remaining**: CVE-2024-12798, CVE-2024-12801 (logback-core 1.5.12) - Latest available version still vulnerable
- **Remaining**: CVE-2025-22233 (Spring Framework 6.1.15) - Latest stable version still vulnerable
- **Remaining**: CVE-2025-22228 (Spring Security 6.3.5) - Latest compatible version still vulnerable

Note: Some vulnerabilities cannot be fixed immediately as they require newer versions that may not be compatible with the current Spring Batch 5.1.2 version.

## Troubleshooting

### NVD API Key Issues
If you see warnings about missing NVD API key:
1. Ensure the environment variable is set: `echo $NVD_API_KEY`
2. The scan will still work without a key but will be much slower

### Database Connection Issues
1. Verify Oracle container is running: `docker ps`
2. Check logs: `docker-compose logs oracledb`
3. Default connection: `localhost:1521/ORCLCDB`

### Build Failures
1. Ensure Java 17 is installed: `java -version`
2. Clear Maven cache: `mvn clean`
3. Update dependencies: `mvn dependency:purge-local-repository`

## License

[Add your license information here]

## Support

For issues and questions, please refer to the project documentation or contact the development team.