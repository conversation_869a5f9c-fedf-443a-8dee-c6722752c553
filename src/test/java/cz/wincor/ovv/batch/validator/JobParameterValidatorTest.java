package cz.wincor.ovv.batch.validator;

import java.util.HashSet;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.JobParametersInvalidException;

import cz.wincor.ovv.batch.BatchConstants;

import static org.junit.jupiter.api.Assertions.assertThrowsExactly;

/**
 * Test class for {@link JobParameterValidator}
 * <AUTHOR>
 *
 */
public class JobParameterValidatorTest {

    private JobParameterValidator validator;

    @BeforeEach
    public void setUp() throws Exception {
        validator = new JobParameterValidator();
    }

    @Test
    public void testValidateSuccessfully() throws Exception {
        Set<String> requiredParameters = new HashSet<>();
        requiredParameters.add(BatchConstants.PARAM_FILE_OUTPUT_COLUMNS);
        requiredParameters.add(BatchConstants.PARAM_FILE_OUTPUT_DELIMITER);
        validator.setRequiredParameters(requiredParameters);
        validator.afterPropertiesSet();
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        jobParametersBuilder.addString(BatchConstants.PARAM_FILE_OUTPUT_COLUMNS, "Test");
        jobParametersBuilder.addLong(BatchConstants.PARAM_FILE_OUTPUT_DELIMITER, 8l);
        //Set extra parameter
        jobParametersBuilder.addString(BatchConstants.PARAM_FILE_TO_UPLOAD, "Test2");
        validator.validate(jobParametersBuilder.toJobParameters());
    }

    @Test
    public void testEmptyValue() throws Exception {
        Set<String> requiredParameters = new HashSet<>();
        requiredParameters.add(BatchConstants.PARAM_FILE_OUTPUT_DELIMITER);
        validator.setRequiredParameters(requiredParameters);
        validator.afterPropertiesSet();
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        jobParametersBuilder.addString(BatchConstants.PARAM_FILE_OUTPUT_DELIMITER, "");

        assertThrowsExactly(JobParametersInvalidException.class, () -> validator.validate(jobParametersBuilder.toJobParameters()));
    }

    @Test
    public void testMissingParameter() throws Exception {
        Set<String> requiredParameters = new HashSet<>();
        requiredParameters.add(BatchConstants.PARAM_FILE_OUTPUT_DELIMITER);
        validator.setRequiredParameters(requiredParameters);
        validator.afterPropertiesSet();
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();

        assertThrowsExactly(JobParametersInvalidException.class, () -> validator.validate(jobParametersBuilder.toJobParameters()));
    }

}
