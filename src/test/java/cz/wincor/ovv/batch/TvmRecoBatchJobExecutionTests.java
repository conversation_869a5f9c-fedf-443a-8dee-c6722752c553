package cz.wincor.ovv.batch;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

import cz.wincor.ovv.batch.service.ReportBatchService;
import cz.wincor.ovv.batch.service.ReportFileService;
import cz.wincor.ovv.batch.service.StoreService;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.batch.admin.service.JobService;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import cz.wincor.ovv.batch.entity.batch.ReportBatch;
import cz.wincor.ovv.batch.entity.batch.ReportFile;
import cz.wincor.ovv.batch.entity.ovv.CountryCode;
import cz.wincor.ovv.batch.entity.ovv.OvvStore;

/**
 * Integration test for tvmRecoJob
 *
 * <AUTHOR>
 *
 */
public class TvmRecoBatchJobExecutionTests extends JobIntegrationTests {

    private static final String BATCH_FILE_NAME = "WN_CZ_TVM_20160825_1460713440000.csv";

    private static final String RECO_FILE_NAME = "WN_CZ_TVM_20160825_1460713440000.csv.result";

    private static final String RECO_FILE_NAME_ZIP = "WN_CZ_TVM_20160825_1460713440000.csv.result.zip";

    @Autowired
    private JobService jobService;

    private static EmbeddedSftpServer server;

    @Autowired
    private ReportFileService reportFileService;

    private String recoInputPath;

    private String recoOutputPath;

    @Value("${batchJob.test.reconciliate.input.path}")
    private String speficicInputPath;

    @Value("${batchJob.test.generate.output.file.path}")
    private String speficicOutputPath;

    @Value("${batch.job.base.path}")
    private String basePath;

    @Value("${batchJob.test.sftp.remoteDirectory.archive}")
    private String sftpArchivePath;

    @Autowired
    private ReportBatchService reportBatchService;

    private static File sftpFolder;

    @Autowired
    private StoreService storeService;

    private OvvStore store;

    @BeforeAll
    public static void startServer() throws Exception {
        server = new EmbeddedSftpServer();
        server.setPort(2223);
        // Starting SFTP
        sftpFolder = Files.createTempDirectory("SFTP_RECO_TEST").toFile();
        server.afterPropertiesSet();
        server.setHomeFolder(sftpFolder);
        if (!server.isRunning()) {
            server.start();
        }

    }

    @AfterAll
    public static void stopServer() {
        if (server.isRunning()) {
            server.stop();
        }

    }

    @BeforeEach
    public void setup() throws Exception {
        recoInputPath = Paths.get(basePath, speficicInputPath).toAbsolutePath().toString();
        recoOutputPath = Paths.get(basePath, speficicOutputPath).toAbsolutePath().toString();
        cleanAndCreateDirectory(recoInputPath);
        cleanAndCreateDirectory(recoOutputPath);
        // Load one CZ store to be used to report batches
        store = storeService.findByCountryCodeAndPartnerIdIn(CountryCode.CZ, Arrays.asList("45308314")).get(0);
    }

    @AfterEach
    public void afterTest() throws IOException {
        FileUtils.cleanDirectory(sftpFolder);
        reportBatchService.deleteAll();
        reportFileService.deleteAll();
    }

    void cleanAndCreateDirectory(String path) throws IOException {
        File directory = new File(path);
        if (directory.exists()) {
            FileUtils.forceDelete(directory);
        }
        FileUtils.forceMkdir(directory);
    }

    /**
     * This test covers the complete reconciliation process of incoming reco
     * files from each issuer
     * <p>
     * SFTP download of reco files is excluded from this test (covered by
     * another test)
     * <p>
     * This test reconciliate incoming file against the DB
     *
     * @throws Exception
     */
    @Test
    public void testLaunchRecoJobWithSuccessResult() throws Exception {
        // ---------------------start of test prepare-----------------------
        // Create a file on remote sftp, it will be used
        File remoteFile = new File(sftpFolder, RECO_FILE_NAME);
        FileUtils.writeStringToFile(remoteFile, "M0500100020;1", "UTF-8");
        File recoResultFile = new File(recoInputPath, RECO_FILE_NAME);
        FileUtils.writeStringToFile(recoResultFile,
                "2\nM0500100020;0\n" + "M0500100021;1;Wrong code\n" + "MISSING_VOUCHER;0\n", "UTF-8");
        // precondition, reco file must exist
        Path tempRecoFile = Files.createTempFile(null, null);
        assertTrue(recoResultFile.exists());
        ReportFile reportFile = new ReportFile();
        reportFile.setUuid(1460713440000l);
        reportFile.setOvvIssuer("TVM");
        reportFile.setFileName(BATCH_FILE_NAME);
        reportFile.setFileNameDate("20160825");
        reportFile.setFilePath(tempRecoFile.toAbsolutePath().toString());
        reportFile.setJobExecutionId(1000l);
        reportFile.setCreated(new Date());
        reportFile.setItemsCount(0);
        reportFile.setCountryCode(CountryCode.CZ);
        reportFile = reportFileService.save(reportFile);
        ReportBatch reportBatch1 = new ReportBatch();
        reportBatch1.setReportFile(reportFile);
        reportBatch1.setVoucherNumber("M0500100020");
        reportBatch1.setOvvStoreId(store.getId());
        reportBatch1 = reportBatchService.save(reportBatch1);
        ReportBatch reportBatch2 = new ReportBatch();
        reportBatch2.setReportFile(reportFile);
        reportBatch2.setVoucherNumber("M0500100021");
        reportBatch2.setOvvStoreId(store.getId());
        reportBatch2 = reportBatchService.save(reportBatch2);

        assertTrue(server.isRunning());
        // Create job parameters
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder()
                .addString(BatchConstants.PARAM_INPUT_FILENAME, recoResultFile.getAbsolutePath())
                .addLong("input.file.linesToSkip", 1l)
                .addString("input.file.columns", "voucherNumber,resultCode,resultDescription")
                .addString("input.file.delimiter", ";").addString("output.path", recoOutputPath)
                .addString(BatchConstants.PARAM_FAIL_ON_MISSING, "false");
        // -----------------------end of test prepare------------------------

        // Launch job
        JobExecution jobExecution = jobService.launch("testRecoJob", jobParametersBuilder.toJobParameters());

        // Non null result
        assertNotNull(jobExecution);

        // The final status must be completed
        assertEquals(BatchStatus.COMPLETED, jobExecution.getStatus());

        // Check whether a report batch has been successfully reconciliated

        assertEquals(0, reportBatchService.getById(reportBatch1.getId()).getResultCode().intValue());
        assertEquals(1, reportBatchService.getById(reportBatch2.getId()).getResultCode().intValue());
        assertEquals("Wrong code", reportBatchService.getById(reportBatch2.getId()).getResultDescription());

        reportFile = reportFileService.getById(reportFile.getId());
        assertEquals(jobExecution.getId(), reportFile.getRecoJobExecutionId());
        assertTrue(jobExecution.getExecutionContext().containsKey(BatchConstants.PARAM_RECO_ERROR_FILE));
        File errorReportFile = (File) jobExecution.getExecutionContext().get(BatchConstants.PARAM_RECO_ERROR_FILE);
        List<String> readAllLines = Files.readAllLines(errorReportFile.toPath(),
                Charset.forName(BatchConstants.CHARSET_UTF8));
        assertEquals(1, readAllLines.size());
        assertThat(readAllLines.get(0), containsString("MISSING_VOUCHER"));
        // Get files from sftp folder
        Collection<File> listFiles = FileUtils.listFiles(sftpFolder, new String[] { "err" }, false);
        // One file must be created
        assertEquals(1, listFiles.size());
        Path archivedFile = Paths.get(sftpFolder.getAbsolutePath(), sftpArchivePath, RECO_FILE_NAME);
        assertTrue(Files.exists(archivedFile), "One file must be archived");

        // List all files in archive directory and check if they are present
        try (Stream< Path>archivedFilesStream = Files.list(Paths.get(recoOutputPath))) {
            assertEquals(1, archivedFilesStream.count());
        }

        // List all files in input directory and check if they are missing
        try (Stream< Path>archivedFilesStream = Files.list(Paths.get(recoInputPath))) {
            assertEquals(0, archivedFilesStream.count());
        }
    }

    /**
     * This test simulated reconciliation of incoming ZIP file (i.e. incoming
     * file needs to be unpacked first)
     *
     * @throws Exception
     */
    @Test
    public void testLaunchRecoJobWithZIPandSuccessResult() throws Exception {
        // ---------------------start of test prepare-----------------------
        // load zip file from classpath and copy it into a temp directory
        InputStream in = this.getClass().getClassLoader().getResourceAsStream("reco-files/" + RECO_FILE_NAME_ZIP);
        Path zipFile = Paths.get(recoInputPath, RECO_FILE_NAME_ZIP);
        Files.copy(in, zipFile, StandardCopyOption.REPLACE_EXISTING);
        assertTrue(Files.exists(zipFile));
        Path sftpZipFile = Paths.get(sftpFolder.getAbsolutePath().toString(), RECO_FILE_NAME_ZIP);
        Files.copy(in, sftpZipFile, StandardCopyOption.REPLACE_EXISTING);
        // precondition, reco file must exist
        Path tempRecoFile = Files.createTempFile(null, null);
        ReportFile reportFile = new ReportFile();
        reportFile.setUuid(1460713440000l);
        reportFile.setOvvIssuer("TVM");
        reportFile.setFileName(BATCH_FILE_NAME);
        reportFile.setFileNameDate("20160825");
        reportFile.setFilePath(tempRecoFile.toAbsolutePath().toString());
        reportFile.setJobExecutionId(1000l);
        reportFile.setCreated(new Date());
        reportFile.setItemsCount(0);
        reportFile.setCountryCode(CountryCode.CZ);
        reportFile = reportFileService.save(reportFile);
        ReportBatch reportBatch1 = new ReportBatch();
        reportBatch1.setReportFile(reportFile);
        reportBatch1.setVoucherNumber("M0500100020");
        reportBatch1.setOvvStoreId(store.getId());
        reportBatch1 = reportBatchService.save(reportBatch1);
        ReportBatch reportBatch2 = new ReportBatch();
        reportBatch2.setReportFile(reportFile);
        reportBatch2.setVoucherNumber("M0500100021");
        reportBatch2.setOvvStoreId(store.getId());
        reportBatch2 = reportBatchService.save(reportBatch2);
        // Create job parameters
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder()
                .addString(BatchConstants.PARAM_INPUT_FILENAME, zipFile.toAbsolutePath().toString())
                .addLong("input.file.linesToSkip", 1l)
                .addString("input.file.columns", "voucherNumber,resultCode,resultDescription")
                .addString("input.file.delimiter", ";").addString("fake", "1").addString("output.path", recoOutputPath)
                .addString(BatchConstants.PARAM_FAIL_ON_MISSING, "false");
        // -----------------------end of test prepare------------------------

        // Launch job
        JobExecution jobExecution = jobService.launch("testRecoJob", jobParametersBuilder.toJobParameters());

        // Non null result
        assertNotNull(jobExecution);

        // The final status must be completed
        assertEquals(BatchStatus.COMPLETED, jobExecution.getStatus());

        Path archivedFile = Paths.get(sftpFolder.getAbsolutePath(), sftpArchivePath, RECO_FILE_NAME_ZIP);
        assertTrue(Files.exists(archivedFile), "One file must be archived");

        // List all files in archive directory and check if they are present
        try (Stream< Path>archivedFilesStream = Files.list(Paths.get(recoOutputPath))) {
            assertEquals(2, archivedFilesStream.count());
        }

        // List all files in input directory and check if they are missing
        try (Stream< Path>archivedFilesStream = Files.list(Paths.get(recoInputPath))) {
            assertEquals(0, archivedFilesStream.count());
        }
    }
}
