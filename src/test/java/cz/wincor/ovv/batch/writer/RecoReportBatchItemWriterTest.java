package cz.wincor.ovv.batch.writer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrowsExactly;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import java.io.File;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.Collections;
import java.util.List;

import cz.wincor.ovv.batch.service.ReportBatchService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionException;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ExecutionContext;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.entity.batch.ReportFile;
import cz.wincor.ovv.batch.pojo.VoucherResponse;

/**
 * Test class for {@link RecoReportBatchItemWriter}
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RecoReportBatchItemWriterTest {

    @Mock
    private JobExecution jobExecution;

    @Mock
    private ReportFile reportFile;

    @Mock
    private ReportBatchService service;

    private ExecutionContext executionContext;

    @InjectMocks
    private RecoReportBatchItemWriter recoReportBatchItemWriter;

    @BeforeEach
    public void setUp() throws Exception {
        executionContext = new ExecutionContext();
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
    }

    @AfterEach
    public void after() throws Exception {
        recoReportBatchItemWriter.setFailOnMissingVoucher(false);
    }

    @Test
    public void testWriteSuccessful() throws Exception {
        VoucherResponse voucherResponse = new VoucherResponse();
        voucherResponse.setResultCode(0);
        voucherResponse.setResultDescription("DESC");
        voucherResponse.setVoucherNumber("000111");

        when(service.setResultToReportBatch(anyLong(), eq(0), eq("DESC"), eq("000111"))).thenReturn(1);
        recoReportBatchItemWriter.write(new Chunk<>(Collections.singletonList(voucherResponse)));
        verify(service).setResultToReportBatch(anyLong(), eq(0), eq("DESC"), eq("000111"));
        verifyNoMoreInteractions(service);
        assertFalse(executionContext.containsKey(BatchConstants.PARAM_RECO_ERROR_FILE));
    }

    @Test
    public void testWriteWithNoDBUpdate() throws Exception {
        Path recoFile = Files.createTempFile("reco", null);
        when(reportFile.getFilePath()).thenReturn(recoFile.toAbsolutePath().toString());
        VoucherResponse voucherResponse = new VoucherResponse();
        voucherResponse.setId(1);
        voucherResponse.setResultCode(0);
        voucherResponse.setResultDescription("DESC");
        voucherResponse.setVoucherNumber("000111");

        when(service.setResultToReportBatch(anyLong(), eq(0), eq("DESC"), eq("000111"))).thenReturn(0);
        recoReportBatchItemWriter.write(new Chunk<>(Collections.singletonList(voucherResponse)));
        assertTrue(executionContext.containsKey(BatchConstants.PARAM_RECO_ERROR_FILE));
    }

    @Test
    public void testWriteWithNoDBUpdateRequiredFail() throws Exception {
        recoReportBatchItemWriter.setFailOnMissingVoucher(true);
        VoucherResponse voucherResponse = new VoucherResponse();
        voucherResponse.setId(1);
        voucherResponse.setResultCode(0);
        voucherResponse.setResultDescription("DESC");
        voucherResponse.setVoucherNumber("000111");

        when(service.setResultToReportBatch(anyLong(), eq(0), eq("DESC"), eq("000111"))).thenReturn(0);

        assertThrowsExactly(JobExecutionException.class, () -> recoReportBatchItemWriter.write(new Chunk<>(Collections.singletonList(voucherResponse))));
    }

    @Test
    public void testAddErrorToExistingFile() throws Exception {
        Path errorReportFile = Files.createTempFile("reco", ".err");
        Files.write(errorReportFile, "ERROR1\n".getBytes(BatchConstants.CHARSET_UTF8), StandardOpenOption.WRITE);
        executionContext.put(BatchConstants.PARAM_RECO_ERROR_FILE, errorReportFile.toFile());
        recoReportBatchItemWriter.addErrorToFile("Test line");
        List<String> readAllLines = Files.readAllLines(errorReportFile, Charset.forName(BatchConstants.CHARSET_UTF8));
        assertEquals(2, readAllLines.size());
        assertEquals("Test line", readAllLines.get(1));
    }

    @Test
    public void testAddErrorToNewFile() throws Exception {
        Path recoFile = Files.createTempFile("reco", null);
        when(reportFile.getFilePath()).thenReturn(recoFile.toAbsolutePath().toString());
        recoReportBatchItemWriter.addErrorToFile("Test line");
        assertTrue(executionContext.containsKey(BatchConstants.PARAM_RECO_ERROR_FILE));
        File errorReportFile = (File)executionContext.get(BatchConstants.PARAM_RECO_ERROR_FILE);
        List<String> readAllLines = Files.readAllLines(errorReportFile.toPath(), Charset.forName(BatchConstants.CHARSET_UTF8));
        assertEquals(1, readAllLines.size());
        assertEquals("Test line", readAllLines.get(0));
    }

}
