package cz.wincor.ovv.batch.scheduler;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.time.Clock;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.batch.core.JobParameter;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.pojo.ReportPeriod;

/**
 * Test for {@link DailyJobScheduler}
 * <AUTHOR>
 *
 */
public class DailyJobSchedulerTest {


    private DailyJobScheduler dailyJobScheduler = new DailyJobScheduler();

    @BeforeEach
    public void setUp() throws Exception {
        dailyJobScheduler.setEnabled(true);
    }

    @Test
    public void testSetParametersSuccessfully() throws Exception {
        Map<String, JobParameter<?>> parameters = new LinkedHashMap<>();
        dailyJobScheduler.setClock(newFixedClock("20170110"));
        dailyJobScheduler.setParameters(parameters);
        assertEquals("20170109", parameters.get(BatchConstants.PARAM_REPORT_START_DATE).getValue());
        assertEquals(ReportPeriod.DAY.toString(), parameters.get(BatchConstants.PARAM_REPORT_PERIOD).getValue());
    }

    /**
     * A date {@link String} to be converted to {@link Clock}
     */
    private Clock newFixedClock(String date) {
        LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.BASIC_ISO_DATE);
        return Clock.fixed(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant(), ZoneId.systemDefault());
    }

}
