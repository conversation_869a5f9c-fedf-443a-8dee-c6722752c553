package cz.wincor.ovv.batch.scheduler;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.time.Clock;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.batch.core.JobParameter;
import org.springframework.batch.core.JobParameters;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.pojo.ReportPeriod;

/**
 * Test for {@link WeeklyJobScheduler}
 * <AUTHOR>
 *
 */
public class WeeklyJobSchedulerTest {


    private WeeklyJobScheduler weeklyJobScheduler = new WeeklyJobScheduler();

    @BeforeEach
    public void setUp() throws Exception {
        weeklyJobScheduler.setEnabled(true);
    }

    @Test
    public void testSetParametersSuccessfully1() throws Exception {
        Map<String, JobParameter<?>> parameters = new LinkedHashMap<>();
        weeklyJobScheduler.setClock(newFixedClock("20170110"));
        weeklyJobScheduler.setParameters(parameters);
        assertEquals("20170102", parameters.get(BatchConstants.PARAM_REPORT_START_DATE).getValue());
        assertEquals(ReportPeriod.WEEK.toString(), parameters.get(BatchConstants.PARAM_REPORT_PERIOD).getValue());
    }

    @Test
    public void testSetParametersSuccessfully2() throws Exception {
        Map<String, JobParameter<?>> parameters = new LinkedHashMap<>();
        weeklyJobScheduler.setClock(newFixedClock("20170103"));
        weeklyJobScheduler.setParameters(parameters);
        assertEquals("20161226", parameters.get(BatchConstants.PARAM_REPORT_START_DATE).getValue());
        assertEquals(ReportPeriod.WEEK.toString(), parameters.get(BatchConstants.PARAM_REPORT_PERIOD).getValue());
    }

    @Test
    public void testSetParametersSuccessfully3() throws Exception {
        Map<String, JobParameter<?>> parameters = new LinkedHashMap<>();
        weeklyJobScheduler.setClock(newFixedClock("20170108"));
        weeklyJobScheduler.setParameters(parameters);
        assertEquals("20161226", parameters.get(BatchConstants.PARAM_REPORT_START_DATE).getValue());
        assertEquals(ReportPeriod.WEEK.toString(), parameters.get(BatchConstants.PARAM_REPORT_PERIOD).getValue());
    }

    /**
     * A date {@link String} to be converted to {@link Clock}
     */
    private Clock newFixedClock(String date) {
        LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.BASIC_ISO_DATE);
        return Clock.fixed(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant(), ZoneId.systemDefault());
    }

}
