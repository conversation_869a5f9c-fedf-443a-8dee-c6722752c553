package cz.wincor.ovv.batch.tasklet;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrowsExactly;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import java.text.SimpleDateFormat;
import java.time.format.DateTimeParseException;
import java.time.zone.ZoneRulesException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameter;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.scope.context.StepContext;
import org.springframework.batch.item.ExecutionContext;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.pojo.ReportPeriod;

/**
 * Test class for {@link OvvBatchIntervalGenerator}
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class OvvBatchIntervalGeneratorTest {

    private OvvBatchIntervalGenerator ovvBatchIntervalGenerator;

    @Mock
    private ChunkContext chunkContext;

    @Mock
    private StepContext stepContext;


    private ExecutionContext executionContext;

    @Mock
    private StepExecution stepExecution;

    @Mock
    private JobExecution jobExecution;

    private Map<String, JobParameter<?>> jobParameters;

    private static final String DATE_TIME_FORMAT = "dd.MM.yyyy HH:mm:ss.SSS";


    @BeforeEach
    public void setUp() throws Exception {
        when(chunkContext.getStepContext()).thenReturn(stepContext);
        when(stepContext.getStepExecution()).thenReturn(stepExecution);
        when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        executionContext = new ExecutionContext();
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
        jobParameters = new HashMap<>();
        ovvBatchIntervalGenerator = new OvvBatchIntervalGenerator();
    }

    @Test
    public void testSuccessfulDayPeriodInPragueTimezone() throws Exception {
        jobParameters.put(BatchConstants.PARAM_REPORT_START_DATE, new JobParameter<>("20111203", String.class));
        jobParameters.put(BatchConstants.PARAM_RECEIVER_TIMEZONE, new JobParameter<>("Europe/Prague", String.class));
        jobParameters.put(BatchConstants.PARAM_REPORT_PERIOD, new JobParameter<>(ReportPeriod.DAY.name(), String.class));
        when(stepExecution.getJobParameters()).thenReturn(new JobParameters(jobParameters));
        ovvBatchIntervalGenerator.execute(null, chunkContext);

        assertTrue(executionContext.containsKey(BatchConstants.PARAM_READER_START));
        assertTrue(executionContext.containsKey(BatchConstants.PARAM_READER_END));
        SimpleDateFormat formatter = new SimpleDateFormat(DATE_TIME_FORMAT);
        assertEquals("03.12.2011 00:00:00.000", formatter.format((Date)executionContext.get(BatchConstants.PARAM_READER_START)));
        assertEquals("03.12.2011 23:59:59.999", formatter.format((Date)executionContext.get(BatchConstants.PARAM_READER_END)));
    }

    @Test
    public void testSuccessfulWeekPeriodInPragueTimezone() throws Exception {
        jobParameters.put(BatchConstants.PARAM_REPORT_START_DATE, new JobParameter<>("20170306", String.class));
        jobParameters.put(BatchConstants.PARAM_RECEIVER_TIMEZONE, new JobParameter<>("Europe/Prague", String.class));
        jobParameters.put(BatchConstants.PARAM_REPORT_PERIOD, new JobParameter<>(ReportPeriod.WEEK.name(), String.class));
        when(stepExecution.getJobParameters()).thenReturn(new JobParameters(jobParameters));
        ovvBatchIntervalGenerator.execute(null, chunkContext);

        assertTrue(executionContext.containsKey(BatchConstants.PARAM_READER_START));
        assertTrue(executionContext.containsKey(BatchConstants.PARAM_READER_END));
        SimpleDateFormat formatter = new SimpleDateFormat(DATE_TIME_FORMAT);
        assertEquals("06.03.2017 00:00:00.000", formatter.format((Date)executionContext.get(BatchConstants.PARAM_READER_START)));
        assertEquals("12.03.2017 23:59:59.999", formatter.format((Date)executionContext.get(BatchConstants.PARAM_READER_END)));
    }

    @Test
    public void testSuccessfulMonthPeriodInPragueTimezone() throws Exception {
        jobParameters.put(BatchConstants.PARAM_REPORT_START_DATE, new JobParameter<>("20170301", String.class));
        jobParameters.put(BatchConstants.PARAM_RECEIVER_TIMEZONE, new JobParameter<>("Europe/Prague", String.class));
        jobParameters.put(BatchConstants.PARAM_REPORT_PERIOD, new JobParameter<>(ReportPeriod.MONTH.name(), String.class));
        when(stepExecution.getJobParameters()).thenReturn(new JobParameters(jobParameters));
        ovvBatchIntervalGenerator.execute(null, chunkContext);

        assertTrue(executionContext.containsKey(BatchConstants.PARAM_READER_START));
        assertTrue(executionContext.containsKey(BatchConstants.PARAM_READER_END));
        SimpleDateFormat formatter = new SimpleDateFormat(DATE_TIME_FORMAT);
        assertEquals("01.03.2017 00:00:00.000", formatter.format((Date)executionContext.get(BatchConstants.PARAM_READER_START)));
        assertEquals("31.03.2017 23:59:59.999", formatter.format((Date)executionContext.get(BatchConstants.PARAM_READER_END)));
    }

    @Test
    public void testSuccessfulInUTCTimezone() throws Exception {
        jobParameters.put(BatchConstants.PARAM_REPORT_START_DATE, new JobParameter<>("20111203", String.class));
        jobParameters.put(BatchConstants.PARAM_RECEIVER_TIMEZONE, new JobParameter<>("UTC", String.class));
        jobParameters.put(BatchConstants.PARAM_REPORT_PERIOD, new JobParameter<>(ReportPeriod.DAY.name(), String.class));
        when(stepExecution.getJobParameters()).thenReturn(new JobParameters(jobParameters));
        ovvBatchIntervalGenerator.execute(null, chunkContext);

        assertTrue(executionContext.containsKey(BatchConstants.PARAM_READER_START));
        assertTrue(executionContext.containsKey(BatchConstants.PARAM_READER_END));
        SimpleDateFormat formatter = new SimpleDateFormat(DATE_TIME_FORMAT);
        assertEquals("03.12.2011 01:00:00.000", formatter.format((Date)executionContext.get(BatchConstants.PARAM_READER_START)));
        assertEquals("04.12.2011 00:59:59.999", formatter.format((Date)executionContext.get(BatchConstants.PARAM_READER_END)));
    }

    @Test
    public void testWrongTimezoneParameter() throws Exception {
        jobParameters.put(BatchConstants.PARAM_REPORT_START_DATE, new JobParameter<>("20111203", String.class));
        jobParameters.put(BatchConstants.PARAM_RECEIVER_TIMEZONE, new JobParameter<>("Europe/XXX", String.class));
        jobParameters.put(BatchConstants.PARAM_REPORT_PERIOD, new JobParameter<>(ReportPeriod.DAY.name(), String.class));
        when(stepExecution.getJobParameters()).thenReturn(new JobParameters(jobParameters));

        assertThrowsExactly(ZoneRulesException.class, () -> ovvBatchIntervalGenerator.execute(null, chunkContext));
    }

    @Test
    public void testMissingJobPeriodParameter() throws Exception {
        jobParameters.put(BatchConstants.PARAM_REPORT_START_DATE, new JobParameter<>("20111203", String.class));
        jobParameters.put(BatchConstants.PARAM_RECEIVER_TIMEZONE, new JobParameter<>("Europe/Prague", String.class));
        when(stepExecution.getJobParameters()).thenReturn(new JobParameters(jobParameters));

        assertThrowsExactly(NullPointerException.class, () -> ovvBatchIntervalGenerator.execute(null, chunkContext));
    }

    @Test
    public void testWrongInputDateParameter() throws Exception {
        jobParameters.put(BatchConstants.PARAM_REPORT_START_DATE, new JobParameter<>("20111203x", String.class));
        jobParameters.put(BatchConstants.PARAM_RECEIVER_TIMEZONE, new JobParameter<>("Europe/Prague", String.class));
        jobParameters.put(BatchConstants.PARAM_REPORT_PERIOD, new JobParameter<>(ReportPeriod.DAY.name(), String.class));
        when(stepExecution.getJobParameters()).thenReturn(new JobParameters(jobParameters));

        assertThrowsExactly(DateTimeParseException.class, () -> ovvBatchIntervalGenerator.execute(null, chunkContext));
    }


}
