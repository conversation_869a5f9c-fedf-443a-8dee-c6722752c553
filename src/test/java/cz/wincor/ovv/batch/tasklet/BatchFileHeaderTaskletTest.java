package cz.wincor.ovv.batch.tasklet;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrowsExactly;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.batch.core.JobExecutionException;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.scope.context.StepContext;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.entity.batch.ReportFile;

/**
 * Test class for {@link BatchFileHeaderTasklet}
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class BatchFileHeaderTaskletTest {

    private BatchFileHeaderTasklet batchFileHeaderTasklet;

    private Map<String, Object> executionContext;

    @Mock
    private ChunkContext chunkContext;

    @Mock
    private StepContext stepContext;

    @BeforeEach
    public void setUp() throws Exception {
        executionContext = new HashMap<String, Object>();
        when(chunkContext.getStepContext()).thenReturn(stepContext);
        when(stepContext.getJobExecutionContext()).thenReturn(executionContext);
        batchFileHeaderTasklet = new BatchFileHeaderTasklet();
    }

    @Test
    public void testSuccessfulFlow() throws Exception {
        Path tempDirectory = Files.createTempDirectory("BATCH");
        // Temp folder must be empty
        assertEquals(0, FileUtils.listFiles(tempDirectory.toFile(), null, false).size());
        Path batchFile = Paths.get(tempDirectory.toAbsolutePath().toString(), "WN_CZ_TVM_20160825_1460713440000.csv");
        String testContent = "" + "123456789;456;456\n" + "987654321;123;321\n" + "654987123;123;963\n";
        Files.write(batchFile, testContent.getBytes(BatchConstants.CHARSET_UTF8), StandardOpenOption.CREATE,
                StandardOpenOption.TRUNCATE_EXISTING);
        ReportFile reportFile = new ReportFile();
        reportFile.setFilePath(batchFile.toAbsolutePath().toString());
        reportFile.setItemsCount(3);
        executionContext.put(BatchConstants.PARAM_REPORT_FILE, reportFile);
        batchFileHeaderTasklet.execute(null, chunkContext);
        // Batch file must exist
        assertTrue(Files.exists(batchFile));
        // Get files (only one must exist)
        assertEquals(1, FileUtils.listFiles(tempDirectory.toFile(), null, false).size());
        // Read all lines from a result batch file
        List<String> lines = FileUtils.readLines(batchFile.toFile());
        assertEquals("3", lines.get(0));
        assertEquals("123456789;456;456", lines.get(1));
    }

    /**
     * Test when temp file already exists (e.g. from a previous run)
     * @throws Exception
     */
    @Test
    public void testSuccessfulFlowWithExistingTempFile() throws Exception {
        Path tempDirectory = Files.createTempDirectory("BATCH");
        // Temp folder must be empty
        assertEquals(0, FileUtils.listFiles(tempDirectory.toFile(), null, false).size());
        Path batchFile = Paths.get(tempDirectory.toAbsolutePath().toString(), "WN_CZ_TVM_20160825_1460713440000.csv");
        Path tempFile = Paths.get(tempDirectory.toAbsolutePath().toString(), "WN_CZ_TVM_20160825_1460713440000.csv.temp");
        String testContent = "" + "123456789;456;456\n" + "987654321;123;321\n" + "654987123;123;963\n";
        Files.write(batchFile, testContent.getBytes(BatchConstants.CHARSET_UTF8), StandardOpenOption.CREATE,
                StandardOpenOption.TRUNCATE_EXISTING);
        Files.write(tempFile, testContent.getBytes(BatchConstants.CHARSET_UTF8), StandardOpenOption.CREATE,
                StandardOpenOption.TRUNCATE_EXISTING);
        ReportFile reportFile = new ReportFile();
        reportFile.setFilePath(batchFile.toAbsolutePath().toString());
        reportFile.setItemsCount(3);
        executionContext.put(BatchConstants.PARAM_REPORT_FILE, reportFile);
        batchFileHeaderTasklet.execute(null, chunkContext);
        // Batch file must exist
        assertTrue(Files.exists(batchFile));
        // Get files (only one must exist)
        assertEquals(1, FileUtils.listFiles(tempDirectory.toFile(), null, false).size());
        // Read all lines from a result batch file
        List<String> lines = FileUtils.readLines(batchFile.toFile());
        assertEquals("3", lines.get(0));
        assertEquals("123456789;456;456", lines.get(1));
    }

    /**
     * Test there is inconsistency between expected number of rows and real number of rows
     * @throws Exception
     */
    @Test
    public void testWrongNumberOfItemsInFile() throws Exception {
        Path tempDirectory = Files.createTempDirectory("BATCH");
        // Temp folder must be empty
        assertEquals(0, FileUtils.listFiles(tempDirectory.toFile(), null, false).size());
        Path batchFile = Paths.get(tempDirectory.toAbsolutePath().toString(), "WN_CZ_TVM_20160825_1460713440000.csv");
        Path tempFile = Paths.get(tempDirectory.toAbsolutePath().toString(), "WN_CZ_TVM_20160825_1460713440000.csv.temp");
        String testContent = "" + "123456789;456;456\n" + "987654321;123;321\n" + "654987123;123;963\n";
        Files.write(batchFile, testContent.getBytes(BatchConstants.CHARSET_UTF8), StandardOpenOption.CREATE,
                StandardOpenOption.TRUNCATE_EXISTING);
        Files.write(tempFile, testContent.getBytes(BatchConstants.CHARSET_UTF8), StandardOpenOption.CREATE,
                StandardOpenOption.TRUNCATE_EXISTING);
        ReportFile reportFile = new ReportFile();
        reportFile.setFilePath(batchFile.toAbsolutePath().toString());
        //Set wrong number of expected items
        reportFile.setItemsCount(2);
        executionContext.put(BatchConstants.PARAM_REPORT_FILE, reportFile);

        assertThrowsExactly(JobExecutionException.class, () -> batchFileHeaderTasklet.execute(null, chunkContext));
    }

    @Test
    public void testMissingBatchFileInContextFlow() throws Exception {
        Path tempDirectory = Files.createTempDirectory("BATCH");
        // Temp folder must be empty
        assertEquals(0, FileUtils.listFiles(tempDirectory.toFile(), null, false).size());
        Path batchFile = Paths.get(tempDirectory.toAbsolutePath().toString(), "WN_CZ_TVM_20160825_1460713440000.csv");
        ReportFile reportFile = new ReportFile();
        reportFile.setFilePath(batchFile.toAbsolutePath().toString());
        reportFile.setItemsCount(2);
        executionContext.put(BatchConstants.PARAM_REPORT_FILE, reportFile);
        //executionContext.put(BatchConstants.PARAM_NUMBER_OF_ITEMS_IN_BATCH, 3);

        assertThrowsExactly(IllegalArgumentException.class, () -> batchFileHeaderTasklet.execute(null, chunkContext));
    }

}
