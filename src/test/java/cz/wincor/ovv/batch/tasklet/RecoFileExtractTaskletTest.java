package cz.wincor.ovv.batch.tasklet;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrowsExactly;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionException;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.scope.context.StepContext;
import org.springframework.batch.item.ExecutionContext;

import cz.wincor.ovv.batch.BatchConstants;

/**
 * Test class for {@link RecoFileExtractTasklet}
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RecoFileExtractTaskletTest {

    private RecoFileExtractTasklet recoFileExtractTasklet;

    private ExecutionContext executionContext;

    @Mock
    private ChunkContext chunkContext;

    @Mock
    private StepContext stepContext;

    @Mock
    private StepExecution stepExecution;

    @Mock
    private JobExecution jobExecution;

    private Map<String, Object> jobParameters = new HashMap<>();

    @BeforeEach
    public void setUp() throws Exception {
        executionContext = new ExecutionContext();
        when(chunkContext.getStepContext()).thenReturn(stepContext);
        when(stepContext.getStepExecution()).thenReturn(stepExecution);
        when(stepContext.getJobParameters()).thenReturn(jobParameters);
        when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
        recoFileExtractTasklet = new RecoFileExtractTasklet();
    }

    @Test
    public void testUnzipSuccessfully() throws Exception {
        Path tempDirectory = Files.createTempDirectory("TEST_UNZIP");
        // load zip file from classpath and copy it into a temp directory
        InputStream in = this.getClass().getClassLoader()
                .getResourceAsStream("reco-files/WN_CZ_TVM_20160825_1460713440000.csv.result.zip");
        Path zipFile = Paths.get(tempDirectory.toAbsolutePath().toString(), "WN_CZ_TVM_20160825_1460713440000.csv.result.zip");
        Files.copy(in, zipFile, StandardCopyOption.REPLACE_EXISTING);
        // Set parameters
        jobParameters.put(BatchConstants.PARAM_INPUT_FILENAME, zipFile.toAbsolutePath().toString());
        recoFileExtractTasklet.execute(null, chunkContext);
        Path extractedFile = Paths.get(tempDirectory.toAbsolutePath().toString(), "WN_CZ_TVM_20160825_1460713440000.csv.result");
        assertTrue(Files.exists(extractedFile), "Extracted RECO file does not exist.");
        assertEquals(extractedFile.toAbsolutePath().toString(),executionContext.get(BatchConstants.PARAM_INPUT_FILENAME));
        List<String> readLines = FileUtils.readLines(extractedFile.toFile());
        assertEquals(4, readLines.size());
    }

    @Test
    public void testUnzipSuccessfullyPreviousRecoAlreadyExist() throws Exception {
        Path tempDirectory = Files.createTempDirectory("TEST_UNZIP");
        // load zip file from classpath and copy it into a temp directory
        InputStream in = this.getClass().getClassLoader()
                .getResourceAsStream("reco-files/WN_CZ_TVM_20160825_1460713440000.csv.result.zip");
        Path zipFile = Paths.get(tempDirectory.toAbsolutePath().toString(), "WN_CZ_TVM_20160825_1460713440000.csv.result.zip");
        Files.copy(in, zipFile, StandardCopyOption.REPLACE_EXISTING);
        Path recoFile = Paths.get(tempDirectory.toAbsolutePath().toString(), "WN_CZ_TVM_20160825_1460713440000.csv.result");
        String testContent = "" + "123456789;456;456\n" + "987654321;123;321\n" + "654987123;123;963";
        Files.write(recoFile, testContent.getBytes(BatchConstants.CHARSET_UTF8), StandardOpenOption.CREATE,
                StandardOpenOption.TRUNCATE_EXISTING);
        // Set parameters
        jobParameters.put(BatchConstants.PARAM_INPUT_FILENAME, zipFile.toAbsolutePath().toString());
        recoFileExtractTasklet.execute(null, chunkContext);
        Path extractedFile = Paths.get(tempDirectory.toAbsolutePath().toString(), "WN_CZ_TVM_20160825_1460713440000.csv.result");
        assertTrue(Files.exists(extractedFile), "Extracted RECO file does not exist.");
    }

    @Test
    public void testSkipUnzipSuccessfully() throws Exception {
        Path tempDirectory = Files.createTempDirectory("BATCH");
        // Temp folder must be empty
        assertEquals(0, FileUtils.listFiles(tempDirectory.toFile(), null, false).size());
        Path recoFile = Paths.get(tempDirectory.toAbsolutePath().toString(), "WN_CZ_TVM_20160825_1460713440000.csv.result");
        String testContent = "" + "123456789;456;456\n" + "987654321;123;321\n" + "654987123;123;963";
        Files.write(recoFile, testContent.getBytes(BatchConstants.CHARSET_UTF8), StandardOpenOption.CREATE,
                StandardOpenOption.TRUNCATE_EXISTING);
        jobParameters.put(BatchConstants.PARAM_INPUT_FILENAME, recoFile.toAbsolutePath().toString());
        recoFileExtractTasklet.execute(null, chunkContext);
        assertEquals(recoFile.toAbsolutePath().toString(),executionContext.get(BatchConstants.PARAM_INPUT_FILENAME));
    }

    @Test
    public void testWrongRecoFilename() throws Exception {
        Path tempDirectory = Files.createTempDirectory("TEST_UNZIP");
        // load zip file from classpath and copy it into a temp directory
        InputStream in = this.getClass().getClassLoader()
                .getResourceAsStream("reco-files/wrong_WN_CZ_TVM_20160825_1460713440000.csv.result.zip");
        Path zipFile = Paths.get(tempDirectory.toAbsolutePath().toString(), "WN_CZ_TVM_20160825_1460713440000.csv.result.zip");
        Files.copy(in, zipFile, StandardCopyOption.REPLACE_EXISTING);
        // Set parameters
        jobParameters.put(BatchConstants.PARAM_INPUT_FILENAME, zipFile.toAbsolutePath().toString());

        assertThrowsExactly(JobExecutionException.class, () -> recoFileExtractTasklet.execute(null, chunkContext));
    }

}
