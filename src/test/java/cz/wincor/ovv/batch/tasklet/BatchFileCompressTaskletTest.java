package cz.wincor.ovv.batch.tasklet;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import java.io.FileInputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;
import java.util.zip.ZipInputStream;

import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.scope.context.StepContext;
import org.springframework.batch.item.ExecutionContext;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.entity.batch.ReportFile;

/**
 * Test class for {@link BatchFileCompressTasklet}
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class BatchFileCompressTaskletTest {

    private BatchFileCompressTasklet batchFileHeaderTasklet;

    private ExecutionContext executionContext;

    @Mock
    private ChunkContext chunkContext;

    @Mock
    private StepContext stepContext;

    @Mock
    private StepExecution stepExecution;

    @Mock
    private JobExecution jobExecution;

    @BeforeEach
    public void setUp() throws Exception {
        executionContext = new ExecutionContext();
        when(chunkContext.getStepContext()).thenReturn(stepContext);
        when(stepContext.getStepExecution()).thenReturn(stepExecution);
        when(stepExecution.getJobExecution()).thenReturn(jobExecution);
        when(jobExecution.getExecutionContext()).thenReturn(executionContext);
        batchFileHeaderTasklet = new BatchFileCompressTasklet();
        batchFileHeaderTasklet.setCompressingEnabled(true);
    }

    @Test
    public void testSuccessfulFlow() throws Exception {
        Path tempDirectory = Files.createTempDirectory("BATCH");
        // Temp folder must be empty
        assertEquals(0, FileUtils.listFiles(tempDirectory.toFile(), null, false).size());
        Path batchFile = Paths.get(tempDirectory.toAbsolutePath().toString(), "WN_CZ_TVM_20160825_1460713440000.csv");
        String testContent = "" + "123456789;456;456\n" + "987654321;123;321\n" + "654987123;123;963";
        Files.write(batchFile, testContent.getBytes(BatchConstants.CHARSET_UTF8), StandardOpenOption.CREATE,
                StandardOpenOption.TRUNCATE_EXISTING);
        ReportFile reportFile = new ReportFile();
        reportFile.setFileName("WN_CZ_TVM_20160825_1460713440000.csv");
        reportFile.setFilePath(batchFile.toAbsolutePath().toString());
        executionContext.put(BatchConstants.PARAM_REPORT_FILE, reportFile);
        batchFileHeaderTasklet.execute(null, chunkContext);
        // Batch file must exist
        assertTrue(Files.exists(batchFile));
        // Get files (old one and a new zip file must exist)
        assertEquals(2, FileUtils.listFiles(tempDirectory.toFile(), null, false).size());
        assertTrue(Files.exists(Paths.get(reportFile.getFilePath() + ".zip")));
        ZipInputStream zis =
                new ZipInputStream(new FileInputStream(reportFile.getFilePath() + ".zip"));
        assertNotNull(zis.getNextEntry(), "zip archive must contain exactly one entry.");
        zis.close();
        assertTrue(executionContext.containsKey(BatchConstants.PARAM_FILE_TO_UPLOAD));
    }

    @Test
    public void testSuccessfulFlowButPreviousZipAlreadyExist() throws Exception {
        Path tempDirectory = Files.createTempDirectory("BATCH");
        // Temp folder must be empty
        assertEquals(0, FileUtils.listFiles(tempDirectory.toFile(), null, false).size());
        Path batchFile = Paths.get(tempDirectory.toAbsolutePath().toString(), "WN_CZ_TVM_20160825_1460713440000.csv");
        String testContent = "" + "123456789;456;456\n" + "987654321;123;321\n" + "654987123;123;963";
        Files.write(batchFile, testContent.getBytes(BatchConstants.CHARSET_UTF8), StandardOpenOption.CREATE,
                StandardOpenOption.TRUNCATE_EXISTING);
        Files.copy(batchFile, Paths.get(batchFile.toAbsolutePath() + ".zip"), StandardCopyOption.REPLACE_EXISTING);
        ReportFile reportFile = new ReportFile();
        reportFile.setFileName("WN_CZ_TVM_20160825_1460713440000.csv");
        reportFile.setFilePath(batchFile.toAbsolutePath().toString());
        executionContext.put(BatchConstants.PARAM_REPORT_FILE, reportFile);
        batchFileHeaderTasklet.execute(null, chunkContext);
        // Batch file must exist
        assertTrue(Files.exists(batchFile));
        // Get files (old one and a new zip file must exist)
        assertEquals(2, FileUtils.listFiles(tempDirectory.toFile(), null, false).size());
        assertTrue(Files.exists(Paths.get(reportFile.getFilePath() + ".zip")));
        ZipInputStream zis =
                new ZipInputStream(new FileInputStream(reportFile.getFilePath() + ".zip"));
        assertNotNull(zis.getNextEntry(), "zip archive must contain exactly one entry.");
        zis.close();
        assertTrue(executionContext.containsKey(BatchConstants.PARAM_FILE_TO_UPLOAD));
    }

}
