package cz.wincor.ovv.batch.tasklet;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Test class for {@link FileArchiveTasklet}
 * <AUTHOR>
 *
 */
public class FileArchiveTaskletTest {

    private FileArchiveTasklet fileArchiveTasklet;

    @BeforeEach
    public void setUp() throws Exception {
        this.fileArchiveTasklet = new FileArchiveTasklet();
    }

    @Test
    public void testSuccessfulMoveWithAbsoluteTargetPath() throws Exception {
        Path tempDirectoryFrom = Files.createTempDirectory("FROM_");
        Path tempDirectoryTo = Files.createTempDirectory("TO_");
        Path file1 = Files.createTempFile(tempDirectoryFrom, "FILE1_", ".txt");
        Path file2 = Files.createTempFile(tempDirectoryFrom, "FILE2_", ".txt");
        Files.write(file1, "FILE1".getBytes());
        Files.write(file2, "FILE2".getBytes());
        assertTrue(Files.exists(file1));
        assertTrue(Files.exists(file2));
        Set<String> filesToMove = new HashSet<>();
        filesToMove.add(file1.toAbsolutePath().toString());
        filesToMove.add(file2.toAbsolutePath().toString());

        fileArchiveTasklet.setArchiveDirectory(tempDirectoryTo.toAbsolutePath().toString());
        fileArchiveTasklet.setFilesToArchive(filesToMove);
        fileArchiveTasklet.afterPropertiesSet();

        fileArchiveTasklet.execute(null, null);

        assertFalse(Files.exists(file1));
        assertFalse(Files.exists(file2));

        //List all files in archive directory and check if they are present
        try (Stream<Path> archivedFilesStream = Files.list(tempDirectoryTo)) {
            Set<Path> archivedFiles = archivedFilesStream.map(path -> path.getFileName()).collect(Collectors.toSet());
            assertEquals(2, archivedFiles.size());
            assertTrue(archivedFiles.contains(file1.getFileName()));
            assertTrue(archivedFiles.contains(file2.getFileName()));
        }

        //Read lines from the first line and check if expected
        try (Stream<String> lines = Files.lines(Paths.get(tempDirectoryTo.toAbsolutePath().toString(), file1.getFileName().toString()))) {
            assertEquals("FILE1", lines.collect(Collectors.toList()).get(0));
        }
    }

    @Test
    public void testSuccessfulMoveWithRelativeTargetPath() throws Exception {
        Path tempDirectoryFrom = Files.createTempDirectory("FROM_");
        Path tempDirectoryTo = Files.createTempDirectory(tempDirectoryFrom, "TO_");
        Path file1 = Files.createTempFile(tempDirectoryFrom, "FILE1_", ".txt");
        Path file2 = Files.createTempFile(tempDirectoryFrom, "FILE2_", ".txt");
        assertTrue(Files.exists(file1));
        assertTrue(Files.exists(file2));
        Set<String> filesToMove = new HashSet<>();
        filesToMove.add(file1.toAbsolutePath().toString());

        fileArchiveTasklet.setArchiveDirectory(tempDirectoryTo.toAbsolutePath().toString());
        fileArchiveTasklet.setFilesToArchive(filesToMove);
        fileArchiveTasklet.afterPropertiesSet();

        fileArchiveTasklet.execute(null, null);

        assertFalse(Files.exists(file1));
      //List all files in archive directory and check if they are present
        try (Stream<Path> archivedFilesStream = Files.list(tempDirectoryTo)) {
            Set<Path> archivedFiles = archivedFilesStream.map(path -> path.getFileName()).collect(Collectors.toSet());
            assertEquals(1, archivedFiles.size());
            assertTrue(archivedFiles.contains(file1.getFileName()));
        }

        //File2 was not marked to be moved, so it should be on the same place
        assertTrue(Files.exists(file2));
    }

    /**
     * This test simulates when a missing file is being to move, so no exception should be thrown
     * @throws Exception
     */
    @Test
    public void testSuccessfulMoveMissingFile() throws Exception {
        Path tempDirectoryFrom = Files.createTempDirectory("FROM_");
        Path tempDirectoryTo = Files.createTempDirectory(tempDirectoryFrom, "TO_");
        Set<String> filesToMove = new HashSet<>();
        Path missingFile = Paths.get(tempDirectoryFrom.toString(), "nonexistingFile.txt");
        filesToMove.add(missingFile.toAbsolutePath().toString());

        assertFalse(Files.exists(missingFile));

        fileArchiveTasklet.setArchiveDirectory(tempDirectoryTo.toAbsolutePath().toString());
        fileArchiveTasklet.setFilesToArchive(filesToMove);
        fileArchiveTasklet.afterPropertiesSet();

        fileArchiveTasklet.execute(null, null);

        assertFalse(Files.exists(missingFile));

      //List all files in archive directory and check if they are present
        try (Stream<Path> archivedFilesStream = Files.list(tempDirectoryTo)) {
            assertEquals(0, archivedFilesStream.count());
        }
    }
}
