package cz.wincor.ovv.batch.support;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;



/**
 * Test for {@link RepeatedFileListFilter}
 *
 * <AUTHOR>
 *
 */
public class RepeatedFileListFilterTest {

    @Test
    public void testDefaultConstructor() {
        RepeatedFileListFilter filter = new RepeatedFileListFilter();
        File file1 = new File("/tmp/test1.txt");
        File file2 = new File("/tmp/test2.txt");
        assertTrue(filter.accept(file1));
        assertTrue(filter.accept(file2));
        assertFalse(filter.accept(file1));
        assertFalse(filter.accept(file2));
    }

    @Test
    public void testCustomRepeatCount() {
        RepeatedFileListFilter filter = new RepeatedFileListFilter(2);
        File file1 = new File("/tmp/test1.txt");
        File file2 = new File("/tmp/test2.txt");
        assertTrue(filter.accept(file1));
        assertTrue(filter.accept(file2));
        assertTrue(filter.accept(file1));
        assertTrue(filter.accept(file2));
        assertFalse(filter.accept(file1));
        assertFalse(filter.accept(file2));
    }

    @Test
    public void testCustomRepeatCountWithPatternMatch() {
        RepeatedFileListFilter filter = new RepeatedFileListFilter(1, "(.*\\.result$)|(.*\\.result.zip$)");
        File file1 = new File("/tmp/test1.result.txt");
        File file2 = new File("/tmp/result.txt");
        File file3 = new File("/result/test.txt");
        File file4 = new File("/tmp/test.txt.result");
        File file5 = new File("/tmp/test.txt.result.zip");
        assertFalse(filter.accept(file1));
        assertFalse(filter.accept(file2));
        assertFalse(filter.accept(file3));
        assertTrue(filter.accept(file4));
        assertTrue(filter.accept(file5));
    }


}
