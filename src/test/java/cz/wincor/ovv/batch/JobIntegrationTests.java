package cz.wincor.ovv.batch;

import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.annotation.DirtiesContext.ClassMode;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(locations = "classpath:/test-context.xml")
@ExtendWith(SpringExtension.class)
@DirtiesContext(classMode = ClassMode.AFTER_CLASS)
public abstract class JobIntegrationTests {

}
