package cz.wincor.ovv.batch;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.concurrent.Callable;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import cz.wincor.ovv.batch.service.ReportBatchService;
import cz.wincor.ovv.batch.service.ReportFileService;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.ContextConfiguration;

import cz.wincor.ovv.batch.support.AdapterControlBusStarter;
import cz.wincor.ovv.batch.util.ZipUtil;

/**
 * Integration test for SFTP download related to the tvmRecoJob
 *
 * <AUTHOR>
 *
 */

@ContextConfiguration(locations = { "classpath:/META-INF/spring/batch/jobs/testRecoJob-context.xml" })
public class TvmRecoSftpDownloadExecutionTests extends JobIntegrationTests {

    private static EmbeddedSftpServer server;

    @Autowired
    private ReportFileService reportFileService;

    private String recoInputPath;

    @Value("${batchJob.test.reconciliate.input.path}")
    private String speficicInputPath;

    @Value("${batch.job.base.path}")
    private String basePath;

    @Autowired
    private ReportBatchService reportBatchService;

    private static File sftpFolder;

    @Autowired
    private AdapterControlBusStarter adapterControlBusStarter;

    @BeforeAll
    public static void startServer() throws Exception {
        server = new EmbeddedSftpServer();
        server.setPort(2223);
        // Starting SFTP
        sftpFolder = Files.createTempDirectory("SFTP_RECO_TEST").toFile();
        server.afterPropertiesSet();
        server.setHomeFolder(sftpFolder);
        if (!server.isRunning()) {
            server.start();
        }

    }

    @AfterAll
    public static void stopServer() {
        if (server.isRunning()) {
            server.stop();
        }

    }

    @BeforeEach
    public void setup() throws Exception {
        recoInputPath = Paths.get(basePath, speficicInputPath).toAbsolutePath().toString();
        cleanAndCreateDirectory(recoInputPath);
    }

    @AfterEach
    public void afterTest() throws IOException {
        adapterControlBusStarter.stop();
        FileUtils.cleanDirectory(sftpFolder);
        reportBatchService.deleteAll();
        reportFileService.deleteAll();
    }

    void cleanAndCreateDirectory(String path) throws IOException {
        File directory = new File(path);
        if (directory.exists()) {
            FileUtils.forceDelete(directory);
        }
        FileUtils.forceMkdir(directory);
    }

    /**
     * This test covers downloading reco files from a remote SFTP server only
     * <p>
     * The reconciliation process is started as well, but its result is not
     * important in this case (so it may end with an error)
     *
     * @throws Exception
     */
    @Test
    public void testDownloadRemoteResultFilesWithSuccessResult() throws Exception {
        // Start of preparation phase
        File remoteFile = new File(sftpFolder, "WN_CZ_TVM_20160825_1460713440000.csv.result");
        FileUtils.writeStringToFile(remoteFile, "M0500100020;1", "UTF-8");
        File remoteFileZip = new File(sftpFolder, "WN_CZ_TVM_20160825_1460713440000.csv.result.zip");
        ZipUtil.pack(remoteFileZip.getAbsolutePath(), remoteFile.getAbsolutePath());
        File fakeFile = new File(sftpFolder, "WN_CZ_TVM_20160825_1460713440000.csv");
        FileUtils.writeStringToFile(fakeFile, "M0500100020", "UTF-8");
        assertTrue(remoteFile.exists());
        assertTrue(remoteFileZip.exists());
        assertTrue(fakeFile.exists());
        // End of preparation phase
        // Starting adapter responsible for regularly checking of a remote SFTP
        // server
        adapterControlBusStarter.start();
        // Is SFTP server running?
        assertTrue(server.isRunning());
        // Run async task to wait for expected files to be downloaded to a file
        // system from a remote SFTP server
        Future<String> future = Executors.newSingleThreadExecutor().submit(new Callable<String>() {
            @Override
            public String call() throws Exception {
                File outputFile = new File(recoInputPath, remoteFile.getName());
                File zipOutputFile = new File(recoInputPath, remoteFileZip.getName());
                while (!outputFile.exists() || !zipOutputFile.exists()) {
                    Thread.sleep(200);
                }
                return FileUtils.readFileToString(outputFile);
            }
        });
        // Check content of text file
        String resposne = future.get(60, TimeUnit.SECONDS);
        assertEquals("M0500100020;1", resposne);
        // not all of remote files should be downloaded
        assertFalse(new File(recoInputPath, fakeFile.getName()).exists(),
                "This file should not be downloaded, possible wron regex.");
    }

}
