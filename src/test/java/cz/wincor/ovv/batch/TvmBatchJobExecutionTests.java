package cz.wincor.ovv.batch;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

import cz.wincor.ovv.batch.service.ReportBatchService;
import cz.wincor.ovv.batch.service.ReportFileService;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.batch.admin.service.JobService;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import cz.wincor.ovv.batch.entity.batch.ReportBatch;
import cz.wincor.ovv.batch.entity.batch.ReportFile;
import cz.wincor.ovv.batch.pojo.ReportPeriod;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Integration test for tvmBatchJob
 * <p>
 * This test simulates the complete generating process of batch files including
 * upload to a remote SFTP server (actually local SFTP server started before the
 * test)
 *
 *
 * <AUTHOR>
 *
 */
public class TvmBatchJobExecutionTests extends JobIntegrationTests {

    @Autowired
    private JobService jobService;

    @Autowired
    private ReportFileService reportFileService;

    private String genOutputPath;

    @Value("${batchJob.test.generate.output.file.path}")
    private String speficicPath;

    @Value("${batch.job.base.path}")
    private String basePath;

    @Autowired
    private ReportBatchService reportBatchService;

    private static File sftpFolder;

    private static EmbeddedSftpServer server;

    void cleanAndCreateDirectory(String path) throws IOException {
        File directory = new File(path);
        if (directory.exists()) {
            FileUtils.forceDelete(directory);
        }
        FileUtils.forceMkdir(directory);
    }

    @BeforeAll
    public static void startServer() throws Exception {
        server = new EmbeddedSftpServer();
        server.setPort(2223);
        // Starting SFTP
        sftpFolder = Files.createTempDirectory("SFTP_BATCH_TEST").toFile();
        server.afterPropertiesSet();
        server.setHomeFolder(sftpFolder);
        if (!server.isRunning()) {
            server.start();
        }

    }

    @AfterAll
    public static void stopServer() {
        if (server.isRunning()) {
            server.stop();
        }

    }

    @BeforeEach
    public void setup() throws Exception {
        genOutputPath = Paths.get(basePath, speficicPath).toAbsolutePath().toString();
        cleanAndCreateDirectory(genOutputPath);
    }

    @AfterEach
    public void afterTest() throws IOException {
        FileUtils.cleanDirectory(sftpFolder);
        reportBatchService.deleteAll();
        reportFileService.deleteAll();
    }

    @Test
    public void testSimpleProperties() throws Exception {
        assertNotNull(jobService);
    }

    /**
     * This test covers the complete process of generating batch files (including remote SFTP upload)
     *
     * @throws Exception
     */
    @Test
    public void testLaunchBatchJobWithSuccessResult() throws Exception {
        assertTrue(server.isRunning());
        File resultFolder = new File(genOutputPath);
        // Recondition, temp folder must exist
        assertTrue(resultFolder.exists());

        // Create job parameters
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder()
                .addString("output.file.columns", "voucherNumber,storeNumber,costCentre,serverDateTimeIsoFormat,posDateTimeIsoFormat")
                .addString(BatchConstants.PARAM_OUTPUT_PATH, resultFolder.getAbsolutePath())
                .addString(BatchConstants.PARAM_OVV_ISSUER, "TVM").addString("output.file.delimiter", ";")
                .addString(BatchConstants.PARAM_REPORT_START_DATE, "20160825")
                .addString(BatchConstants.PARAM_REPORT_PERIOD, ReportPeriod.DAY.name())
                .addLong("pageSize", 100l)
                .addString(BatchConstants.PARAM_OUTPUT_FILENAME_COUNTRY, "CZ")
                .addString(BatchConstants.PARAM_PARTNER_ID, "45308314")
                .addString(BatchConstants.PARAM_RECEIVER_TIMEZONE, "Europe/Prague");

        // Launch job
        JobExecution jobExecution = jobService.launch("testBatchJob", jobParametersBuilder.toJobParameters());

        // Non null result
        assertNotNull(jobExecution);

        // The final status must be completed
        assertEquals(BatchStatus.COMPLETED, jobExecution.getStatus());

        // Job contains 7 steps
        assertEquals(7, jobExecution.getStepExecutions().size());

        //Expected start Thu Aug 25 00:00:00 CEST 2016 - 1472076000000
        assertEquals("1472076000000", "" + ((Date)jobExecution.getExecutionContext().get(BatchConstants.PARAM_READER_START)).getTime());

        // Report file is saved in db
        ReportFile reportFile = reportFileService.findByJobExecutionId(jobExecution.getId());
        assertNotNull(reportFile);
        //There are two vouchers related to this file
        assertEquals(2, reportFile.getItemsCount().intValue());

        // Report batches are saved in db
        List<ReportBatch> reportBatches = reportBatchService.findByReportFile(reportFile);
        assertEquals(2, reportBatches.size());

        // Get files from local file system
        Collection<File> listFiles = FileUtils.listFiles(resultFolder, new String[] { "csv" }, false);
        // One file must be created
        assertEquals(1, listFiles.size());

        // Filename must meet the following regex
        assertTrue(Pattern.matches("WN_CZ_TVM_20160825_[0-9]{13}\\.csv", listFiles.iterator().next().getName()));

        List<String> readLines = FileUtils.readLines(listFiles.iterator().next());

        // Report file contains one row
        assertEquals(3, readLines.size());

        // The row is equal to ...
        assertEquals("2", readLines.get(0));
        //London time zone
        assertEquals("M0500100020;11027;1106;2016-08-25T12:32:59+02:00;2016-08-25T12:31:59+02:00", readLines.get(1));

        // Get files from sftp folder
        listFiles = FileUtils.listFiles(sftpFolder, new String[] { "zip" }, false);
        // One file must be created
        assertEquals(1, listFiles.size());
    }
}
