package cz.wincor.ovv.batch;



import java.util.TreeSet;

import org.junit.jupiter.api.Test;
import org.springframework.batch.core.configuration.ListableJobLocator;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * This test an existency of all required jobs
 * <AUTHOR>
 *
 */
public class JobListTest extends JobIntegrationTests {

    @Autowired
    private ListableJobLocator jobLocator;

    @Test
    public void testSimpleProperties() throws Exception {
        assertNotNull(jobLocator);
        assertEquals(
                "[czEdenredBatchJob, czEdenredRecoJob, czLchdBatchJob, czLchdRecoJob, czSodexoBatchJob, czSodexoBatchJobPilotDaily, czSodexoBatchJobPilotWeekly, czSodexoRecoJob, czTvm<PERSON>atch<PERSON><PERSON>, czTvm<PERSON>eco<PERSON><PERSON>, huE<PERSON><PERSON><PERSON><PERSON><PERSON>ob, huEdenredRecoJob, huTvmBatchJob, huTvmRecoJob, plLchdBatchJob, plLchdRecoJob, plSodexoBatchJob, plSodexoRecoJob, plTvmBatchJob, plTvmRecoJob, plWasaBatchJob, plWasaRecoJob, skDoxxBatchJob, skDoxxRecoJob, skEdenredGiftBatchJob, skEdenredMealBatchJob, skEdenredRecoJob, skLchdBatchJob, skLchdRecoJob, skTvmBatchJob, skTvmRecoJob, skWasaBatchJob, skWasaRecoJob, testBatchJob, testRecoJob]",
                new TreeSet<String>(jobLocator.getJobNames()).toString());
    }

}
