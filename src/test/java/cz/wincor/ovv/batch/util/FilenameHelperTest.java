package cz.wincor.ovv.batch.util;


import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * Test class for {@link FilenameHelper}
 *
 * <AUTHOR>
 *
 */
public class FilenameHelperTest {

    @Test
    public void testParseSuccessfully() {
        FilenameHelper parsedValue = FilenameHelper.parseFromString("WN_CZ_MEALVO_20160414_1460713440000.csv");
        assertEquals("MEALVO", parsedValue.getIssuer());
        assertEquals("CZ", parsedValue.getCountry());
        assertEquals(1460713440000l, parsedValue.getUuid());
        assertEquals("20160414", parsedValue.getReportDate());
        assertEquals("csv", parsedValue.getSuffix1());
        assertNull(parsedValue.getSuffix2());

        parsedValue = FilenameHelper.parseFromString("WN_CZ_MEALVO_20160414_1460713440000.csv.err");
        assertEquals("MEALVO", parsedValue.getIssuer());
        assertEquals("CZ", parsedValue.getCountry());
        assertEquals(1460713440000l, parsedValue.getUuid());
        assertEquals("20160414", parsedValue.getReportDate());
        assertEquals("csv", parsedValue.getSuffix1());
        assertEquals("err", parsedValue.getSuffix2());
    }

    @Test
    public void testParseMissingSuffix() {
        assertThrows(IllegalArgumentException.class, () -> FilenameHelper.parseFromString("WN_CZ_MEALVO_20160414_1460713440000"));
    }

    @Test
    public void testParseMissingUuid() {
        assertThrows(IllegalArgumentException.class, () -> FilenameHelper.parseFromString("WN_CZ_MEALVO_20160414.csv"));
    }

    @Test
    public void testParseWrongUuidFormat() {
        assertThrows(IllegalArgumentException.class, () -> FilenameHelper.parseFromString("WN_CZ_MEALVO_20160414_dsda.csv"));
    }

    @Test
    public void testEmptyInput() {
        assertThrows(IllegalArgumentException.class, () -> FilenameHelper.parseFromString(""));
    }
}
