<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog logicalFilePath="db.changelog.xml"
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="pavel.sklenar" id="spring_batch_init"
        objectQuotingStrategy="QUOTE_ALL_OBJECTS">
        <sqlFile path="file:db/sql/spring-batch/schema-oracle.sql" dbms="oracle" />
        <sqlFile path="file:db/sql/spring-batch/schema-h2.sql" dbms="h2"/>
        <sqlFile path="file:db/sql/spring-batch/schema-postgresql.sql" dbms="postgresql" />
        <rollback>
            <sqlFile path="file:db/sql/spring-batch/schema-drop-oracle.sql" dbms="oracle"/>
            <sqlFile path="file:db/sql/spring-batch/schema-drop-h2.sql" dbms="h2"/>
            <sqlFile path="file:db/sql/spring-batch/schema-drop-postgresql.sql" dbms="postgresql"/>
        </rollback>
    </changeSet>
    <changeSet author="pavel.sklenar" id="init_app_schema"
        objectQuotingStrategy="QUOTE_ALL_OBJECTS">
        <sqlFile path="file:db/sql/batch/000-schema-oracle.sql" dbms="oracle"/>
        <sqlFile path="file:db/sql/batch/000-schema-h2.sql" dbms="h2"/>
    </changeSet>
    <changeSet author="pavel.sklenar" id="init_ovv_schema"
        objectQuotingStrategy="QUOTE_ALL_OBJECTS">
        <sqlFile path="ovv-schema-h2.sql" dbms="h2"
            relativeToChangelogFile="true" />
    </changeSet>
    <changeSet author="pavel.sklenar" id="init_ovv_data"
        objectQuotingStrategy="QUOTE_ALL_OBJECTS">
        <sqlFile path="ovv-data-store.sql" dbms="h2"
            relativeToChangelogFile="true" />
    </changeSet>
    <changeSet author="pavel.sklenar" id="insert-tranasaction">
        <insert tableName="OVV_TRANSACTION_REQUEST">
            <column name="id" valueNumeric="1" />
            <column name="device_id" value="POS125" />
            <column name="device_local_date_time" valueDate="2016-08-25T12:31:59" />
            <column name="module" value="ovv" />
            <column name="server_local_date_time" valueDate="2016-08-25T12:32:59" />
            <column name="server_utc_date_time" valueDate="2016-08-25T10:32:59" />
            <column name="amount" valueNumeric="1500" />
            <column name="client_ip_address" value="127.0.0.1" />
            <column name="country_code" value="CZ" />
            <column name="pos_country_code" valueNumeric="203" />
            <column name="pos_currency_code" valueNumeric="203" />
            <column name="partner_id" value="45308314" />
            <column name="payment_place" value="11027" />
            <column name="transaction_type" value="VALIDATION" />
            <column name="voucher_number" value="M0500100020" />
            <column name="ovv_issuer" value="TVM" />
            <column name="repeated" valueBoolean="false" />
            <column name="offline_mode" valueBoolean="false" />
            <column name="stan" valueNumeric="1255" />
        </insert>
        <insert tableName="OVV_TRANSACTION_RESPONSE">
            <column name="id" valueNumeric="2" />
            <column name="device_id" value="POS125" />
            <column name="device_local_date_time" valueDate="2016-08-25T12:31:59" />
            <column name="module" value="ovv" />
            <column name="result_code" value="APPROVED" />
            <column name="server_local_date_time" valueDate="2016-08-25T12:32:59" />
            <column name="server_utc_date_time" valueDate="2016-08-25T10:32:59" />
            <column name="related_request_id" valueNumeric="1" />
        </insert>
        <insert tableName="VOUCHER">
            <column name="id" valueNumeric="1" />
            <column name="country_code" value="CZ" />
            <column name="server_local_date_time" valueDate="2016-08-25T12:32:59" />
            <column name="voucher_number" value="M0500100020" />
            <column name="voucher_state" value="USED" />
            <column name="related_transaction_request_id" valueNumeric="1" />
        </insert>
        <insert tableName="OVV_TRANSACTION_REQUEST">
            <column name="id" valueNumeric="3" />
            <column name="device_id" value="POS125" />
            <column name="device_local_date_time" valueDate="2016-08-25T12:50:59" />
            <column name="module" value="ovv" />
            <column name="server_local_date_time" valueDate="2016-08-25T12:51:59" />
            <column name="server_utc_date_time" valueDate="2016-08-25T10:51:59" />
            <column name="amount" valueNumeric="1500" />
            <column name="client_ip_address" value="127.0.0.1" />
            <column name="country_code" value="CZ" />
            <column name="pos_country_code" valueNumeric="203" />
            <column name="pos_currency_code" valueNumeric="203" />
            <column name="partner_id" value="45308314" />
            <column name="payment_place" value="11027" />
            <column name="transaction_type" value="VALIDATION" />
            <column name="voucher_number" value="M0500100021" />
            <column name="ovv_issuer" value="TVM" />
            <column name="repeated" valueBoolean="false" />
            <column name="offline_mode" valueBoolean="false" />
            <column name="stan" valueNumeric="1255" />
        </insert>
        <insert tableName="OVV_TRANSACTION_RESPONSE">
            <column name="id" valueNumeric="4" />
            <column name="device_id" value="POS125" />
            <column name="device_local_date_time" valueDate="2016-08-25T12:50:59" />
            <column name="module" value="ovv" />
            <column name="result_code" value="APPROVED" />
            <column name="server_local_date_time" valueDate="2016-08-25T12:51:59" />
            <column name="server_utc_date_time" valueDate="2016-08-25T10:51:59" />
            <column name="related_request_id" valueNumeric="3" />
        </insert>
        <insert tableName="VOUCHER">
            <column name="id" valueNumeric="2" />
            <column name="country_code" value="CZ" />
            <column name="server_local_date_time" valueDate="2016-08-25T12:51:59" />
            <column name="voucher_number" value="M0500100021" />
            <column name="voucher_state" value="USED" />
            <column name="related_transaction_request_id" valueNumeric="3" />
        </insert>
    </changeSet>
    <changeSet author="pavel.sklenar" id="insert-last_job_executions">
        <insert tableName="BATCH_JOB_INSTANCE">
            <column name="JOB_INSTANCE_ID" valueNumeric="1000" />
            <column name="VERSION" valueNumeric="0" />
            <column name="JOB_NAME" value="tvmBatchJob" />
            <column name="JOB_KEY" value="dbe70ff7eff8bdcd3c070fbfd72f2610" />
        </insert>
        <insert tableName="BATCH_JOB_EXECUTION">
            <column name="JOB_EXECUTION_ID" valueNumeric="1000" />
            <column name="VERSION" valueNumeric="2" />
            <column name="JOB_INSTANCE_ID" valueNumeric="1000" />
            <column name="CREATE_TIME" valueDate="2016-08-26 15:17:30.017" />
            <column name="START_TIME" valueDate="2016-08-26 15:17:30.056" />
            <column name="END_TIME" valueDate="2016-08-26 15:17:30.385" />
            <column name="STATUS" value="COMPLETED" />
            <column name="EXIT_CODE" value="COMPLETED" />
            <column name="EXIT_MESSAGE" />
            <column name="LAST_UPDATED" valueDate="2016-08-26 15:17:30.388" />
        </insert>

    </changeSet>

</databaseChangeLog>
