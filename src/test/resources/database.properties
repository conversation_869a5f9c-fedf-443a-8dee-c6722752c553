##Batch (internal) database setting
batch.data.source.init=false
##Database setting
#Database vendor, possible values: DB2, DERBY, H2, HSQL, INFORMIX, MYSQL, ORACLE, POSTGRESQL, SQL_SERVER, SYBASE,
#required value
batch.database.vendor = H2
#Related Driver class name, required
batch.jdbc.driver=org.h2.Driver
#Database URL, required
batch.jdbc.url=jdbc:h2:mem:test;DB_CLOSE_DELAY=-1
#Database username, required
batch.jdbc.user=sa
#Database password, required
batch.jdbc.password=sa
#Whether real SQL commands should be logged
#Possible values:true, false
#Optional, default value: false
batch.database.showSql=true
batch.database.incrementer.class=org.springframework.jdbc.support.incrementer.H2SequenceMaxValueIncrementer

##OVV Database setting (for reading from tansaction log)
ovv.jdbc.driver=org.h2.Driver
ovv.jdbc.url=jdbc:h2:mem:test;DB_CLOSE_DELAY=-1
ovv.jdbc.user=sa
ovv.jdbc.password=sa
