
    create sequence ovv_barcode_definition_seq start with 1 increment by 1;

    create sequence ovv_store_seq start with 1 increment by 1;

    create sequence transaction_seq start with 1 increment by 1;

    create sequence voucher_seq start with 1 increment by 1;

    create table OVV_BARCODE_DEFINITION (
        id bigint not null,
        BAR_DESC varchar(255),
        bar_len varchar(255),
        BAR_PREF_FROM varchar(255),
        BAR_PREF_TO varchar(255),
        end_date timestamp,
        issuer_country varchar(16) not null,
        PREF_LEN varchar(255),
        start_date timestamp,
        primary key (id)
    );

    create table OVV_STORE (
        id bigint not null,
        cost_centre varchar(255) not null,
        country_code varchar(2) not null,
        name varchar(255) not null,
        partner_id varchar(255),
        site_code varchar(255) not null,
        primary key (id)
    );

    create table OVV_TRANSACTION_REQUEST (
        id bigint not null,
        device_id varchar(255) not null,
        device_local_date_time timestamp not null,
        module varchar(255) not null,
        server_local_date_time timestamp not null,
        server_utc_date_time timestamp not null,
        amount bigint,
        client_ip_address varchar(255),
        country_code varchar(16) not null,
        offline_mode boolean not null,
        ovv_issuer varchar(255),
        partner_id varchar(255),
        payment_place varchar(255),
        pos_country_code smallint,
        pos_currency_code smallint,
        repeated boolean not null,
        stan integer,
        transaction_type varchar(32) not null,
        voucher_number varchar(255),
        previous_request_id bigint,
        primary key (id)
    );

    create table OVV_TRANSACTION_RESPONSE (
        id bigint not null,
        device_id varchar(255) not null,
        device_local_date_time timestamp not null,
        module varchar(255) not null,
        server_local_date_time timestamp not null,
        server_utc_date_time timestamp not null,
        error_description varchar(255),
        result_code varchar(32) not null,
        validation_host varchar(255),
        related_request_id bigint not null,
        primary key (id)
    );

    create table VOUCHER (
        id bigint not null,
        country_code varchar(16) not null,
        kapytimestamp timestamp,
        server_local_date_time timestamp not null,
        voucher_number varchar(255) not null,
        voucher_state varchar(32) not null,
        related_transaction_request_id bigint not null,
        primary key (id)
    );

    alter table OVV_STORE 
        add constraint UKctv2dy91nhwst2hrucq3e52kk unique (country_code, partner_id, site_code);

    create index IDX_server_local_date_time on OVV_TRANSACTION_REQUEST (server_local_date_time);

    create index IDX_COU_CODE_ISSUER_V_NUMBER on OVV_TRANSACTION_REQUEST (country_code, ovv_issuer, voucher_number);

    alter table OVV_TRANSACTION_REQUEST 
        add constraint FKkldym2hk8w5u8j1a4yr3cdlkm 
        foreign key (previous_request_id) 
        references OVV_TRANSACTION_REQUEST;

    alter table OVV_TRANSACTION_RESPONSE 
        add constraint UK_RELATED_REQUEST_ID unique (related_request_id);

    alter table OVV_TRANSACTION_RESPONSE 
        add constraint FKex37iv7etu7a8b3esh4v25i3y 
        foreign key (related_request_id) 
        references OVV_TRANSACTION_REQUEST;

    create index IDX_VOUCHER_COUNTRY_V_NUMBER on VOUCHER (country_code, voucher_number);

    alter table VOUCHER 
        add constraint FKkwltuxxg9axe7if0y16s7f6fe 
        foreign key (related_transaction_request_id) 
        references OVV_TRANSACTION_REQUEST;
