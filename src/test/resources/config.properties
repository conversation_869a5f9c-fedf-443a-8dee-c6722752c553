#Concurrency limit to some heavy tasks
#Set the maximum number of parallel accesses allowed. -1 indicates no concurrency limit at all.
#Default value: current number of available processors
#batch.async.availableProcessors=1

#Configuration for xml files with job definitions, this is not path to *.properties files
#Do not change this value in mostly cases
#Path ${ovv.batch.base.directory} could be set with system property name ovv.batch.base.directory
#If system property ovv.batch.base.directory is missing, java.io.tmpdir will be used instead
batch.job.configuration.file.dir=target/config

batch.job.base.path=${java.io.tmpdir}


