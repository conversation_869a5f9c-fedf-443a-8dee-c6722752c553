<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
    xmlns:jdbc="http://www.springframework.org/schema/jdbc" xmlns:util="http://www.springframework.org/schema/util"
    xmlns:int="http://www.springframework.org/schema/integration"
    xsi:schemaLocation="http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc.xsd
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd
        http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd">

    <import resource="classpath*:/META-INF/spring/batch/bootstrap/**/*.xml" />
    <import resource="classpath*:/META-INF/spring/batch/override/**/*.xml" />

    <bean id="myLiquibase" class="liquibase.integration.spring.SpringLiquibase">
        <property name="dataSource" ref="dataSource" />
        <property name="changeLog" value="classpath:main.db.changelog.xml" />
        <property name="dropFirst" value="true" />
    </bean>

    <bean id="placeholderProperties" parent="placeholderPropertiesParent">
    <property name="locations">
            <list>
                <value>classpath:/org/springframework/batch/admin/bootstrap/batch.properties
                </value>
                <value>${CONFIGURATION_FILE:classpath:/conf/*.properties}</value>
                <value>classpath:/*.properties</value>
            </list>
        </property>
    </bean>

    <int:channel id="job-requests" />

    <bean id="jobLauncherTaskExecutor" class="org.springframework.core.task.SyncTaskExecutor" />
    <!-- prevent loading of other jobs by overriding the loader in the main
        bootstrap context -->
    <!--<bean id="jobLoader" class="java.lang.String"/> -->


    <!-- Use the same db for test to read from transaction log -->
    <bean id="testingDataSource" parent="batchDataSourceParent">
          <property name="connectionFactoryClassName" value="org.h2.jdbcx.JdbcDataSource"/>
    </bean>

    <bean id="batchDataSource" parent="testingDataSource" />
    <bean id="ovvDataSource" parent="testingDataSource" />

</beans>
