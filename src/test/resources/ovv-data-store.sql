insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4081', 'CZ', '45308314', '<PERSON><PERSON><PERSON>','14212');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1801', 'CZ', '45308314', 'Praha Palladium','18001');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2024', 'CZ', '45308314', 'V<PERSON>li nad Luznici','14034');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1299', 'CZ', '45308314', '<PERSON><PERSON><PERSON>','11062');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2113', 'CZ', '45308314', 'Skutec','14154');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1194', 'CZ', '45308314', 'Klatovy','11049');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1143', 'CZ', '45308314', 'Kolin','11025');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2050', 'CZ', '45308314', 'Studenka','14050');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1202', 'CZ', '45308314', 'Brno Herspice','11001');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1304', 'CZ', '45308314', 'Jaromer','11072');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5516', 'CZ', '45308314', 'Karlovy Vary Rybare','14015');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1604', 'CZ', '45308314', 'Brno Videnska','11035');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5519', 'CZ', '45308314', 'Steti','14017');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4033', 'CZ', '45308314', 'Praha Cakovice','14200');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5502', 'CZ', '45308314', 'Bilina','14027');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1601', 'CZ', '45308314', 'Praha Eden','11032');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4057', 'CZ', '45308314', 'Osek','14047');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4017', 'CZ', '45308314', 'Praha Belocerkevska','14197');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2100', 'CZ', '45308314', 'Kamenice Zelivec','14206');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2029', 'CZ', '45308314', 'Plana','14102');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1153', 'CZ', '45308314', 'Jicin','11028');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5510', 'CZ', '45308314', 'Usti nad Labem','14009');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4104', 'CZ', '45308314', 'Jablunkov Pod Alzbetinkami','14118');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5511', 'CZ', '45308314', 'Plzen Doubravka','14028');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4079', 'CZ', '45308314', 'Praha Kodanska','14146');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1101', 'CZ', '45308314', 'Most','11013');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5506', 'CZ', '45308314', 'Chomutov','14005');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4022', 'CZ', '45308314', 'Praha Makovskeho','14048');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2011', 'CZ', '45308314', 'Praha Luka','14032');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1608', 'CZ', '45308314', 'Ostrava','11039');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1173', 'CZ', '45308314', 'Rychnov nad Kneznou','11044');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2015', 'CZ', '45308314', 'Vodnany','14031');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4070', 'CZ', '45308314', 'Praha Starodubecska','14140');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5514', 'CZ', '45308314', 'Jablonec nad Nisou','14016');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4002', 'CZ', '45308314', 'Cernosice','14131');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2041', 'CZ', '45308314', 'Horni Slavkov','14162');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2014', 'CZ', '45308314', 'Sobeslav','14121');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1145', 'CZ', '45308314', 'Opava','11022');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1602', 'CZ', '45308314', 'Praha Novy Smichov','11033');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1605', 'CZ', '45308314', 'Brno Kralovo Pole','11036');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5515', 'CZ', '45308314', 'Sluknov','14012');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1315', 'CZ', '45308314', 'Holesov','11065');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5007', 'CZ', '45308314', 'Liberec Forum','12007');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5524', 'CZ', '45308314', 'Rokytnice nad Jizerou','14024');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4029', 'CZ', '45308314', 'Usti n L Spitalske nam','14069');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1246', 'CZ', '45308314', 'Brandys nad Labem','11122');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2020', 'CZ', '45308314', 'Policka','14111');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1338', 'CZ', '45308314', 'Prelouc','11078');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1244', 'CZ', '45308314', 'Valasske Mezirici','11059');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2030', 'CZ', '45308314', 'Roztoky','14123');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2016', 'CZ', '45308314', 'Bechyne','14030');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2039', 'CZ', '45308314', 'Praha Brandlova','14171');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1108', 'CZ', '45308314', 'Prostejov','11008');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4008', 'CZ', '45308314', 'Praha Taborska','14041');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1211', 'CZ', '45308314', 'Hradec Kralove','11006');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2105', 'CZ', '45308314', 'Chomutov Chomutovka','14081');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1320', 'CZ', '45308314', 'Vysoke Myto','11071');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1106', 'CZ', '45308314', 'Ceske Budejovice','11027');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2108', 'CZ', '45308314', 'Jilemnice','14203');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5513', 'CZ', '45308314', 'Semily','14011');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1126', 'CZ', '45308314', 'Ostrava Hrabova','11009');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1124', 'CZ', '45308314', 'Karlovy Vary','11014');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1607', 'CZ', '45308314', 'Olomouc C4','11038');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2028', 'CZ', '45308314', 'Krupka','14112');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5507', 'CZ', '45308314', 'Melnik Veslarska','14006');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4054', 'CZ', '45308314', 'Praha Na Piskach','14046');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2086', 'CZ', '45308314', 'Cercany','14076');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1177', 'CZ', '45308314', 'Jirkov','11045');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2135', 'CZ', '45308314', 'Rajhrad','14136');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1241', 'CZ', '45308314', 'Breclav','11048');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4090', 'CZ', '45308314', 'Ostrava Hlavni trida','14148');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1275', 'CZ', '45308314', 'Boskovice','11070');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4137', 'CZ', '45308314', 'Trinec Dukelska','14096');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4069', 'CZ', '45308314', 'Ostrava Zamecka','14149');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4040', 'CZ', '45308314', 'Kladno Rozdelov','14059');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4058', 'CZ', '45308314', 'Praha Prokopova','14036');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2049', 'CZ', '45308314', 'Chodov','14052');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1333', 'CZ', '45308314', 'Celakovice','11052');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1116', 'CZ', '45308314', 'Jihlava','11012');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1168', 'CZ', '45308314', 'Pisek','11043');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5509', 'CZ', '45308314', 'Liberec','14008');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5518', 'CZ', '45308314', 'Roudnice nad Labem','14014');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2002', 'CZ', '45308314', 'Mikulov','14001');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5520', 'CZ', '45308314', 'Benesov nad Ploucnici','14018');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1132', 'CZ', '45308314', 'Tabor','11017');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4016', 'CZ', '45308314', 'Brno Spalicek','14042');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1293', 'CZ', '45308314', 'As','11144');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2040', 'CZ', '45308314', 'Litovel','14163');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1180', 'CZ', '45308314', 'Ostrov nad Ohri','11141');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4105', 'CZ', '45308314', 'Cesky Tesin Kysucka','14105');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4106', 'CZ', '45308314', 'Bystrice nad Olsi','14095');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1270', 'CZ', '45308314', 'Vlasim','11067');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5006', 'CZ', '45308314', 'OD Plzen','12006');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4060', 'CZ', '45308314', 'Praha Roztylske namesti','14054');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2154', 'CZ', '45308314', 'Bruntal Cihelni','14084');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1162', 'CZ', '45308314', 'Kromeriz','11020');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1105', 'CZ', '45308314', 'Frydek Mistek','11007');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1216', 'CZ', '45308314', 'Trinec','11046');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1201', 'CZ', '45308314', 'Praha Zlicin','11002');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1268', 'CZ', '45308314', 'Susice','11061');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2081', 'CZ', '45308314', 'Kdyne','14202');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2042', 'CZ', '45308314', 'Zubri','14191');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2006', 'CZ', '45308314', 'Prerov Komuna','14142');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1191', 'CZ', '45308314', 'Unicov','11112');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1112', 'CZ', '45308314', 'Praha Skalka','11029');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4042', 'CZ', '45308314', 'Praha Prazskeho povstani','14071');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1109', 'CZ', '45308314', 'Kladno','11016');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4005', 'CZ', '45308314', 'Dolni Brezany','14172');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4044', 'CZ', '45308314', 'Praha Svobodova','14057');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4006', 'CZ', '45308314', 'Praha Hradcanska','14195');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2054', 'CZ', '45308314', 'Frydlant','14073');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1265', 'CZ', '45308314', 'Chrudim','11057');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1205', 'CZ', '45308314', 'Ostrava Trebovice','11003');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1280', 'CZ', '45308314', 'Sternberk','11060');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4028', 'CZ', '45308314', 'Praha Arbesovo namesti','14049');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5504', 'CZ', '45308314', 'Neratovice','14003');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1137', 'CZ', '45308314', 'Praha Novodvorska','11030');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1134', 'CZ', '45308314', 'Cheb','11018');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5525', 'CZ', '45308314', 'Turnov','14025');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1123', 'CZ', '45308314', 'Karvina','11015');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4007', 'CZ', '45308314', 'Rosice u Brna','14040');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5527', 'CZ', '45308314', 'Zruc nad Sazavou','14021');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1311', 'CZ', '45308314', 'Litvinov','11073');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1609', 'CZ', '45308314', 'Plzen C4','11040');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1210', 'CZ', '45308314', 'Plzen','11005');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1182', 'CZ', '45308314', 'Vyskov','11050');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1190', 'CZ', '45308314', 'Rakovnik','11132');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4113', 'CZ', '45308314', 'Frenstat pod Radhostem Horni','14090');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1203', 'CZ', '45308314', 'Praha Letnany','11004');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1156', 'CZ', '45308314', 'Pribram','11056');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1135', 'CZ', '45308314', 'Melnik','11019');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1142', 'CZ', '45308314', 'Prerov','11021');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1169', 'CZ', '45308314', 'Sokolov','11055');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1188', 'CZ', '45308314', 'Marianske Lazne','11131');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1229', 'CZ', '45308314', 'Cesky Tesin','11051');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2047', 'CZ', '45308314', 'Telc','14194');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2099', 'CZ', '45308314', 'Liberec Ruprechtice','14079');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2017', 'CZ', '45308314', 'Frydek Slezska','14033');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4043', 'CZ', '45308314', 'Praha Konevova','14201');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1104', 'CZ', '45308314', 'Havirov','11010');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2186', 'CZ', '45308314', 'Kunratice','14216');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2094', 'CZ', '45308314', 'Zlin Dlouha','14070');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2097', 'CZ', '45308314', 'Slavicin','14193');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5001', 'CZ', '45308314', 'OD Praha','12001');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4018', 'CZ', '45308314', 'Praha Nuselska','14198');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1611', 'CZ', '45308314', 'Zlin C4','11042');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4011', 'CZ', '45308314', 'Praha Mendelova','14044');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2048', 'CZ', '45308314', 'Duchcov','14038');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4138', 'CZ', '45308314', 'Zlin Malenovice','14135');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2033', 'CZ', '45308314', 'Milovice','14161');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1367', 'CZ', '45308314', 'Trutnov','11145');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5002', 'CZ', '45308314', 'OD Brno','12002');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4135', 'CZ', '45308314', 'Karvina trida Tereskovove','14089');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2045', 'CZ', '45308314', 'Rumburk','14183');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1189', 'CZ', '45308314', 'Novy Jicin','11063');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1321', 'CZ', '45308314', 'Ivancice','11053');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5505', 'CZ', '45308314', 'Decin Bynov','14004');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4147', 'CZ', '45308314', 'Praha Sokolovska','14144');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2064', 'CZ', '45308314', 'Tisnov','14205');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4110', 'CZ', '45308314', 'Frydek Mistek Jiriho Trnky','14128');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2092', 'CZ', '45308314', 'Pohorelice','14037');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1110', 'CZ', '45308314', 'Decin','11011');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4095', 'CZ', '45308314', 'Brno Malinovskeho','14159');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1328', 'CZ', '45308314', 'Mlada Boleslav','11080');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1154', 'CZ', '45308314', 'Kralupy','11023');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2031', 'CZ', '45308314', 'Brno Julianov','14103');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1199', 'CZ', '45308314', 'Ricany','11111');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2072', 'CZ', '45308314', 'Pecky','14074');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1258', 'CZ', '45308314', 'Koprivnice','11054');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1150', 'CZ', '45308314', 'Uhersky Brod','11024');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5523', 'CZ', '45308314', 'Novy Bor','14023');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4015', 'CZ', '45308314', 'Ceske Budejovice Krajinska','14045');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1230', 'CZ', '45308314', 'Klasterec nad Ohri','11101');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2133', 'CZ', '45308314', 'Praha Smichovska trznice','14204');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4001', 'CZ', '45308314', 'Praha Belehradska','14101');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5005', 'CZ', '45308314', 'OD Pardubice','12005');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5004', 'CZ', '45308314', 'OD Hradec Kralove','12004');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1240', 'CZ', '45308314', 'Cesky Krumlov','11121');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4112', 'CZ', '45308314', 'Trinec Sosnova 409','14108');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2032', 'CZ', '45308314', 'Vimperk','14035');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2077', 'CZ', '45308314', 'Habartov','14072');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1160', 'CZ', '45308314', 'Ceska Trebova','11031');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1155', 'CZ', '45308314', 'Zatec','11047');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1606', 'CZ', '45308314', 'Hradec Kralove C4','11037');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1278', 'CZ', '45308314', 'Tachov','11068');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1152', 'CZ', '45308314', 'Uherske Hradiste','11026');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4087', 'CZ', '45308314', 'Libeznice','14158');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1225', 'CZ', '45308314', 'Brno Bohunice','11143');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2095', 'CZ', '45308314', 'Praha KVET','14058');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1253', 'CZ', '45308314', 'Usti nad Orlici','11142');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4013', 'CZ', '45308314', 'Praha Moskevska','14199');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1341', 'CZ', '45308314', 'Hostivice','11146');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1306', 'CZ', '45308314', 'Podebrady','11075');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2091', 'CZ', '45308314', 'Stochov','14056');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1214', 'CZ', '45308314', 'Dvur Kralove','11102');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4046', 'CZ', '45308314', 'Bela pod Bezdezem','14080');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4027', 'CZ', '45308314', 'Sazava','14196');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5526', 'CZ', '45308314', 'Mimon','14020');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1203', 'HU', '10307078', 'Miskolc Extra','41450');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1209', 'HU', '10307078', 'Tatabanya','41590');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1139', 'HU', '10307078', 'Marcali','41031');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6121', 'HU', '10307078', 'Szeged Riverside','44016');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1098', 'HU', '10307078', 'Karcag','41013');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1206', 'HU', '10307078', 'Sopron','41530');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7031', 'HU', '10307078', 'SM 3018 Sopron IV Laszlo','45010');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1401', 'HU', '10307078', 'Budapest Budaors','41520');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1069', 'HU', '10307078', 'Satoraljaujhely','41007');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1074', 'HU', '10307078', 'Hatvan','41024');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1201', 'HU', '10307078', 'Szeged Extra','41420');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1013', 'HU', '10307078', 'Esztergom','41630');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7218', 'HU', '10307078', 'Budapest XV Hubay Jeno','44066');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6168', 'HU', '10307078', 'Mezobereny','44026');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1057', 'HU', '10307078', 'Szarvas','41910');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6116', 'HU', '10307078', 'Nyiregyhaza Domus','44010');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1062', 'HU', '10307078', 'Mateszalka','41005');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7038', 'HU', '10307078', 'SM 3050 Sopron Jozsef Attila','45012');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6100', 'HU', '10307078', 'Veszprem 1k','44003');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1114', 'HU', '10307078', 'Szeged Szabadkai Extra','41029');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7138', 'HU', '10307078', 'Budapest Rakoczi','44054');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7006', 'HU', '10307078', 'SM 1230 Gyor Ipar','45019');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6207', 'HU', '10307078', 'Tahitotfalu','44040');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1111', 'HU', '10307078', 'Bonyhad','41016');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1029', 'HU', '10307078', 'Ajka','41770');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1025', 'HU', '10307078', 'Paks','41820');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7018', 'HU', '10307078', 'SM 2205 Movar Szent Istvan','45004');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1012', 'HU', '10307078', 'Godollo','41720');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1059', 'HU', '10307078', 'Mako','41960');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7130', 'HU', '10307078', 'Miskolc Makropolis','44052');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1046', 'HU', '10307078', 'Kalocsa','41870');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6156', 'HU', '10307078', 'Kunszentmiklos','44025');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1018', 'HU', '10307078', 'Cegled','41680');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7188', 'HU', '10307078', 'Budapest XI Fehervari','44079');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6127', 'HU', '10307078', 'Debrecen Jozsa','44020');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7023', 'HU', '10307078', 'Movar Szent Istvan 44','45005');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7025', 'HU', '10307078', 'SM 3003 Sopron Kiraly Jeno','45006');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1124', 'HU', '10307078', 'Dunakeszi Foti','41058');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1064', 'HU', '10307078', 'Ozd','41002');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1001', 'HU', '10307078', 'Budapest Fogarasi Extra','41400');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7012', 'HU', '10307078', 'Gyor Hedervari','45002');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6160', 'HU', '10307078', 'Vasarosnameny','44034');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6143', 'HU', '10307078', 'Pilis','44023');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7142', 'HU', '10307078', 'Szeged Tisza','44057');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1105', 'HU', '10307078', 'Vecses','41015');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1097', 'HU', '10307078', 'Budapest Hengermalom','41009');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6112', 'HU', '10307078', 'Bekescsaba Lencses','44012');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7003', 'HU', '10307078', 'SM 1217 Gyor Bartok','45016');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7004', 'HU', '10307078', 'SM 1220 Gyor Hunyadi','45017');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6136', 'HU', '10307078', 'Sumeg','44021');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1142', 'HU', '10307078', 'Budapest Becsi','41025');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1021', 'HU', '10307078', 'Budapest South Campona','41710');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7116', 'HU', '10307078', 'Budapest Szent Laszlo','44044');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6148', 'HU', '10307078', 'Albertirsa','44031');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6114', 'HU', '10307078', 'Szombathely 1K','44001');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7030', 'HU', '10307078', 'SM 3011 Sopron Hatsokapu','45007');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1045', 'HU', '10307078', 'Tapolca','41900');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7133', 'HU', '10307078', 'Budapest Thaly Kalman','44029');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1210', 'HU', '10307078', 'Szekszard','41600');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1039', 'HU', '10307078', 'Szazhalombatta','41840');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1170', 'HU', '10307078', 'Debrecen Airport','41990');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6125', 'HU', '10307078', 'Dorog','44018');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7237', 'HU', '10307078', 'Budapest Rakoczi Astoria','44080');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7368', 'HU', '10307078', 'Budapest Hasadek','44082');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1109', 'HU', '10307078', 'Szerencs','41036');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1090', 'HU', '10307078', 'Kormend','41012');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1150', 'HU', '10307078', 'Vac','41047');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1147', 'HU', '10307078', 'Nagyatad','41044');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7371', 'HU', '10307078', 'Siofok Dozsa Gyorgy','44070');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1061', 'HU', '10307078', 'Kisvarda','41810');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1009', 'HU', '10307078', 'Zalaegerszeg','41610');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7105', 'HU', '10307078', 'Budapest Andor','44033');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7148', 'HU', '10307078', 'Budapest Ady Endre','44048');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1055', 'HU', '10307078', 'Hajduszoboszlo','41028');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1008', 'HU', '10307078', 'Dunaujvaros','41700');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1303', 'HU', '10307078', 'Budapest Vaci Extra','41640');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7122', 'HU', '10307078', 'Budapest Beke','44049');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1144', 'HU', '10307078', 'Sarvar','41033');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7202', 'HU', '10307078', 'Miskolc Szechenyi','44060');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1204', 'HU', '10307078', 'Kecskemet','41480');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6117', 'HU', '10307078', 'Szeged Domus','44007');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6003', 'HU', '10307078', 'Gyor Herman','43002');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1002', 'HU', '10307078', 'Szombathely','41500');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1031', 'HU', '10307078', 'Szentes','41830');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1110', 'HU', '10307078', 'Gyula','41026');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7132', 'HU', '10307078', 'Budapest Kisfaludy','44047');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1037', 'HU', '10307078', 'Komarom','41800');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1141', 'HU', '10307078', 'Pecs Kincses','41052');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7191', 'HU', '10307078', 'Budapest IX Kalvin','44067');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1130', 'HU', '10307078', 'Dunakeszi','41041');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1126', 'HU', '10307078', 'Kecskemet Izsaki','41027');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7110', 'HU', '10307078', 'Budapest Bartok Bela','44042');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1302', 'HU', '10307078', 'Pecs','41470');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7120', 'HU', '10307078', 'Budapest XXI Kossuth','44055');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6130', 'HU', '10307078', 'Ullo','44017');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1011', 'HU', '10307078', 'Baja','41650');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7017', 'HU', '10307078', 'SM 2201 Movar Magyar','45003');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1121', 'HU', '10307078', 'Berettyoujfalu','41040');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6107', 'HU', '10307078', 'Budapest Pagony','44005');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7193', 'HU', '10307078', 'Paty','44061');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1036', 'HU', '10307078', 'Szekesfehervar Palota','41780');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1101', 'HU', '10307078', 'Szekesfehervar','41430');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7206', 'HU', '10307078', 'Budapest VIII Nepszinhaz','44063');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1038', 'HU', '10307078', 'Keszthely','41003');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1006', 'HU', '10307078', 'Nagykanizsa','41550');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7230', 'HU', '10307078', 'Debrecen Piac','44081');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6173', 'HU', '10307078', 'Szekszard Arany Janos','44030');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1134', 'HU', '10307078', 'Budakeszi Szoloskert','41037');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1402', 'HU', '10307078', 'Budapest Megapark','41540');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7197', 'HU', '10307078', 'Budapest II Mariaremetei','44078');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6150', 'HU', '10307078', 'Sulysap','44035');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1079', 'HU', '10307078', 'Kazincbarcika','41006');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7201', 'HU', '10307078', 'Budapest VI Eiffel','44062');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7136', 'HU', '10307078', 'Budapest Madzsar','44046');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1072', 'HU', '10307078', 'Csorna','41920');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1161', 'HU', '10307078', 'Budapest Garam','41046');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1138', 'HU', '10307078', 'Nyirbator','41053');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7005', 'HU', '10307078', 'SM 1225 Gyor Koztelek','45018');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1014', 'HU', '10307078', 'Gyongyos','41730');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1048', 'HU', '10307078', 'Budapest Csepel','41890');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7127', 'HU', '10307078', 'Budapest Kolosy','44056');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1091', 'HU', '10307078', 'Oroszlany','41018');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6002', 'HU', '10307078', 'Gyor Lajta','43001');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1137', 'HU', '10307078', 'Sarbogard','41038');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1004', 'HU', '10307078', 'Budapest Pesti ut5','41560');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6208', 'HU', '10307078', 'Isaszeg','44076');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1028', 'HU', '10307078', 'Tata','41011');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1042', 'HU', '10307078', 'Budapest Soroksari','41008');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7369', 'HU', '10307078', 'Dunaujvaros Bercsenyi','44069');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1053', 'HU', '10307078', 'Balassagyarmat','41019');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1015', 'HU', '10307078', 'Mosonmagyarovar','41670');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1026', 'HU', '10307078', 'Kiskunfelegyhaza','41940');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1205', 'HU', '10307078', 'Gyor','41570');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6161', 'HU', '10307078', 'Szolnok Szandaszolos','44050');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1301', 'HU', '10307078', 'Debrecen Extra','41460');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7103', 'HU', '10307078', 'Budapest Bimbo','44037');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6132', 'HU', '10307078', 'Ocsa','44022');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1084', 'HU', '10307078', 'Varpalota','41004');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1050', 'HU', '10307078', 'Erd','41970');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7139', 'HU', '10307078', 'Budapest Pablo Neruda','44051');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6001', 'HU', '10307078', 'Gyor Szigethy','43004');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1093', 'HU', '10307078', 'Koszeg','41060');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1068', 'HU', '10307078', 'Dabas','41022');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6119', 'HU', '10307078', 'Miskolc Domus','44011');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7242', 'HU', '10307078', 'Martonvasar','44089');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7118', 'HU', '10307078', 'Budapest Rakosi','44045');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1003', 'HU', '10307078', 'Eger','41490');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7378', 'HU', '10307078', 'Budapest Nagy Lajos 75','44083');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6123', 'HU', '10307078', 'Szeged Makkoshazi','44013');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1019', 'HU', '10307078', 'Siofok','41660');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1034', 'HU', '10307078', 'Salgotarjan','41760');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6133', 'HU', '10307078', 'Turkeve','44015');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6139', 'HU', '10307078', 'Pecs Volan','44019');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1160', 'HU', '10307078', 'Dunaharaszti','41039');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1157', 'HU', '10307078', 'Balatonfured','41045');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6159', 'HU', '10307078', 'Gyomro','44028');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1010', 'HU', '10307078', 'Hodmezovasarhely','41620');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1158', 'HU', '10307078', 'Budapest Koki','41059');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6147', 'HU', '10307078', 'Szolnok Szechenyi','44036');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1113', 'HU', '10307078', 'Mohacs','41042');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7152', 'HU', '10307078', 'Budapest Thokoly','44087');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6129', 'HU', '10307078', 'Rackeve','44014');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1123', 'HU', '10307078', 'Dombovar','41017');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7036', 'HU', '10307078', 'SM 3043 Sopron Vegfordulat','45011');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1005', 'HU', '10307078', 'Veszprem','41510');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1033', 'HU', '10307078', 'Komlo','41850');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7196', 'HU', '10307078', 'Budapest VII Bajza','44041');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1136', 'HU', '10307078', 'Szigetvar','41034');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6101', 'HU', '10307078', 'Zirc','44004');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6153', 'HU', '10307078', 'Sarkad','44024');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6004', 'HU', '10307078', 'Gyor Mecs Laszlo','43003');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1007', 'HU', '10307078', 'Bekescsaba','41580');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1016', 'HU', '10307078', 'Szolnok Extra','41950');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1020', 'HU', '10307078', 'Papa','41740');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1056', 'HU', '10307078', 'Oroshaza','41690');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1127', 'HU', '10307078', 'Balatonboglar','41021');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7232', 'HU', '10307078', 'Budapest XIII Reitter Ferenc','44006');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1086', 'HU', '10307078', 'Mezokovesd','41001');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7636', 'HU', '10307078', 'Torokbalint','44090');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6187', 'HU', '10307078', 'Eger Agria Park','44032');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2001', 'HU', '10307078', 'Budapest Polus Extra','41390');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1075', 'HU', '10307078', 'Siklos','41930');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6137', 'HU', '10307078', 'Kiskunlachaza','44027');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7114', 'HU', '10307078', 'Budapest Csillaghegyi','44043');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1125', 'HU', '10307078', 'Monor','41043');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1030', 'HU', '10307078', 'Jaszbereny','41790');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1154', 'HU', '10307078', 'Bicske','41049');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1106', 'HU', '10307078', 'Miskolc Mesztelep','41020');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1107', 'HU', '10307078', 'Budapest Arena Plaza','41014');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2011', 'HU', '10307078', 'Kaposvar','41410');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1080', 'HU', '10307078', 'Pecs 3','41980');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7125', 'HU', '10307078', 'Budapest Erzsebet125','44053');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'6194', 'HU', '10307078', 'Celldomolk','44058');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7223', 'HU', '10307078', 'Budapest II Galoca','44064');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7024', 'HU', '10307078', 'SM 2216 Level','45015');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1024', 'HU', '10307078', 'Kiskunhalas','41750');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7227', 'HU', '10307078', 'Dunaharaszti Fo','44077');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1100', 'HU', '10307078', 'Nagykoros','41010');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1202', 'HU', '10307078', 'Nyiregyhaza','41440');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1108', 'HU', '10307078', 'Tokol','41030');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1047', 'HU', '10307078', 'Kiskoros','41880');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1116', 'HU', '10307078', 'Tiszafured','41051');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7372', 'HU', '10307078', 'Balatonfoldvar Budapesti','44071');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7027', 'HU', '10307078', 'SM 3006 Sopron Lackner','45009');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'7208', 'HU', '10307078', 'Budapest III Vihar','44065');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1058', 'HU', '10307078', 'Tiszaujvaros','41860');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1236', 'SK', '31321828', 'Malacky','21047');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4101', 'SK', '31321828', 'Bratislava Hotel City','24029');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2009', 'SK', '31321828', 'Svidnik','24003');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4097', 'SK', '31321828', 'Bratislava Brestovka','24150');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2034', 'SK', '31321828', 'Martin Priekopa','24143');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2074', 'SK', '31321828', 'Ivanka pri Dunaji','24116');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4090', 'SK', '31321828', 'Michalovce Spitalska','24072');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4056', 'SK', '31321828', 'Zilina Vlcince','24061');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2028', 'SK', '31321828', 'Kralovsky Chlmec','24132');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5007', 'SK', '31321828', 'OD Bratislava','22001');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2068', 'SK', '31321828', 'Stara Tura','24017');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1206', 'SK', '31321828', 'Bratislava Petrzalka','21004');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1186', 'SK', '31321828', 'Filakovo','21036');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2072', 'SK', '31321828', 'Smizany','24042');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2016', 'SK', '31321828', 'Kolarovo','24010');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4034', 'SK', '31321828', 'Bratislava Zahorska Bystrica','24026');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1245', 'SK', '31321828', 'Bratislava OC Dubrawa','21057');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2108', 'SK', '31321828', 'Trstice','24013');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4029', 'SK', '31321828', 'Bratislava OD Slimak','24038');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4075', 'SK', '31321828', 'Bratislava Americke','24128');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2106', 'SK', '31321828', 'Banska Stiavnica','24121');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4092', 'SK', '31321828', 'Banska Bystrica Strieborne Nam','24070');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2082', 'SK', '31321828', 'Poltar','24144');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1216', 'SK', '31321828', 'Detva','21050');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4007', 'SK', '31321828', 'Bratislava Kresankova','24016');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2036', 'SK', '31321828', 'Gelnica','24021');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2006', 'SK', '31321828', 'Zilina Hajik','24103');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4051', 'SK', '31321828', 'Cana','24057');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1229', 'SK', '31321828', 'Hlohovec','21038');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1187', 'SK', '31321828', 'Trebisov','21132');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2083', 'SK', '31321828', 'Sladkovicovo','24067');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1209', 'SK', '31321828', 'Bratislava Lamac','21019');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1102', 'SK', '31321828', 'Prievidza','21007');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5011', 'SK', '31321828', 'OD Kosice','22005');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1147', 'SK', '31321828', 'Skalica','21111');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1220', 'SK', '31321828', 'Devinska Nova Ves','21052');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4071', 'SK', '31321828', 'Poprad Podtatranska','24127');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2073', 'SK', '31321828', 'Spisska Bela','24046');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1142', 'SK', '31321828', 'Kezmarok','21032');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2023', 'SK', '31321828', 'Samorin','24122');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2095', 'SK', '31321828', 'Brezova pod Bradlom','24129');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1167', 'SK', '31321828', 'Podunajske Biskupice','21101');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1231', 'SK', '31321828', 'Spisska Nova Ves','21037');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1137', 'SK', '31321828', 'Vranov nad Toplou','21025');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1132', 'SK', '31321828', 'Nove Mesto','21024');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2047', 'SK', '31321828', 'Cierna Voda','24145');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1222', 'SK', '31321828', 'Myjava I','21035');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4031', 'SK', '31321828', 'Bratislava Sevcenkova','24052');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2114', 'SK', '31321828', 'Komjatice','24151');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1129', 'SK', '31321828', 'Dubnica','21018');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2116', 'SK', '31321828', 'Poprad OC Forum','24157');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2066', 'SK', '31321828', 'Gabcikovo','24039');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4004', 'SK', '31321828', 'Bratislava Martincekova','24020');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4060', 'SK', '31321828', 'Bratislava Jeseniova','24147');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4102', 'SK', '31321828', 'Bratislava Racianska','24033');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1139', 'SK', '31321828', 'Ziar nad Hronom','21029');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2035', 'SK', '31321828', 'Hnusta','24014');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2029', 'SK', '31321828', 'Hrinova','24123');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2018', 'SK', '31321828', 'Tornala','24113');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1114', 'SK', '31321828', 'Martin','21009');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2100', 'SK', '31321828', 'Trencianske Teplice','24104');

insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2057', 'SK', '31321828', 'Martin Ladoven','24041');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2081', 'SK', '31321828', 'Zvolen Sekier','24047');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1162', 'SK', '31321828', 'Ruzomberok','21043');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1106', 'SK', '31321828', 'Presov','21010');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1103', 'SK', '31321828', 'Zilina','21006');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2050', 'SK', '31321828', 'Turcianske Teplice','24035');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5012', 'SK', '31321828', 'OD Presov','22006');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2053', 'SK', '31321828', 'Kuty','24114');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4096', 'SK', '31321828', 'Trnava Spacinska','24133');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2123', 'SK', '31321828', 'Nesvady','24158');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2115', 'SK', '31321828', 'Strazske','24074');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2058', 'SK', '31321828', 'Zilina Tempo','24028');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4058', 'SK', '31321828', 'Sucany Robotec','24066');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2107', 'SK', '31321828', 'Ruzomberok OC Adria','24032');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1188', 'SK', '31321828', 'Liptovsky Mikulas','21046');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2004', 'SK', '31321828', 'Vrable','24001');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4003', 'SK', '31321828', 'Bratislava Bieloruska','24022');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1104', 'SK', '31321828', 'Trnava','21005');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1133', 'SK', '31321828', 'Senica','21023');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2033', 'SK', '31321828', 'Medzilaborce','24141');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4023', 'SK', '31321828', 'Bratislava Kosicka','24024');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2075', 'SK', '31321828', 'Zvolen Europa','24044');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2064', 'SK', '31321828', 'Secovce','24040');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2013', 'SK', '31321828', 'Surany','24005');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1185', 'SK', '31321828', 'Kosice','21112');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2015', 'SK', '31321828', 'Tvrdosin','24004');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4054', 'SK', '31321828', 'Pata','24062');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4011', 'SK', '31321828', 'Bratislava Vienna Gate','24007');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2077', 'SK', '31321828', 'Hurbanovo','24115');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2101', 'SK', '31321828', 'Varin','24149');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2008', 'SK', '31321828', 'Stropkov','24006');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4008', 'SK', '31321828', 'Bratislava Stromova','24018');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1122', 'SK', '31321828', 'Michalovce','21013');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1163', 'SK', '31321828', 'Cadca','21122');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4001', 'SK', '31321828', 'Bratislava Komarnicka','24015');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2010', 'SK', '31321828', 'Krupina','24002');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2026', 'SK', '31321828', 'Velke Kapusany','24012');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2021', 'SK', '31321828', 'Moldava','24101');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2122', 'SK', '31321828', 'Bratislava OC Vajnoria','24155');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2060', 'SK', '31321828', 'Bratislava Malokarpatske','24045');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1184', 'SK', '31321828', 'Senec','21102');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1144', 'SK', '31321828', 'Brezno','21030');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1168', 'SK', '31321828', 'Galanta','21039');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1149', 'SK', '31321828', 'Presov Vukov','21044');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2032', 'SK', '31321828', 'Rajec','24142');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2103', 'SK', '31321828', 'Stara Lubovna','24030');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1218', 'SK', '31321828', 'Revuca','21045');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1219', 'SK', '31321828', 'Rimavska Sobota','21051');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2085', 'SK', '31321828', 'Krasno nad Kysucou','24058');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1138', 'SK', '31321828', 'Roznava','21027');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1128', 'SK', '31321828', 'Pezinok','21021');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1119', 'SK', '31321828', 'Nove Zamky','21015');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4066', 'SK', '31321828', 'Bratislava Hraniciarov','24069');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1226', 'SK', '31321828', 'Zvolen','21048');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2124', 'SK', '31321828', 'Rohoznik','24153');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1200', 'SK', '31321828', 'Dunajska Streda','21041');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1136', 'SK', '31321828', 'Dolny Kubin','21031');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1208', 'SK', '31321828', 'Banska Bystrica','21003');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1141', 'SK', '31321828', 'Banovce nad Bebravou','21033');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2093', 'SK', '31321828', 'Tlmace','24117');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4063', 'SK', '31321828', 'Lednicke Rovne','24068');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1143', 'SK', '31321828', 'Humenne','21028');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1302', 'SK', '31321828', 'Bratislava Zlate Piesky','21014');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1130', 'SK', '31321828', 'Povazska Bystrica','21022');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1135', 'SK', '31321828', 'Zlate Moravce','21121');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1140', 'SK', '31321828', 'Puchov','21131');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2102', 'SK', '31321828', 'Bosany','24118');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'5010', 'SK', '31321828', 'OD Nitra','22004');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4022', 'SK', '31321828', 'Bratislava Blagoevova','24025');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1223', 'SK', '31321828', 'Bardejov','21054');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1108', 'SK', '31321828', 'Poprad','21012');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2118', 'SK', '31321828', 'Nova Dubnica','24148');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1146', 'SK', '31321828', 'Snina','21049');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1148', 'SK', '31321828', 'Lucenec','21034');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1134', 'SK', '31321828', 'Partizanske','21026');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1118', 'SK', '31321828', 'Levice','21016');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2117', 'SK', '31321828', 'Bratislava Bzovicka','24152');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1237', 'SK', '31321828', 'Velky Meder','21040');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1127', 'SK', '31321828', 'Sala','21017');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4110', 'SK', '31321828', 'Bratislava Zitavska','24154');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1253', 'SK', '31321828', 'Komarno Abacus','21056');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1210', 'SK', '31321828', 'Trencin','21008');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2017', 'SK', '31321828', 'Krompachy','24102');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2025', 'SK', '31321828', 'Turzovka','24011');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4030', 'SK', '31321828', 'Bratislava Majernikova','24056');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4067', 'SK', '31321828', 'Bratislava Perla Ruzinova','24065');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2056', 'SK', '31321828', 'Prievidza Prior','24037');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1117', 'SK', '31321828', 'Piestany','21011');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1101', 'SK', '31321828', 'Nitra','21001');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1242', 'SK', '31321828', 'Zilina Obvodova','21053');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1120', 'SK', '31321828', 'Topolcany','21020');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'1204', 'SK', '31321828', 'Kosice Trolejbusova','21002');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'4033', 'SK', '31321828', 'Bratislava Svoradova','24054');
insert into OVV_STORE (ID, COST_CENTRE, COUNTRY_CODE, PARTNER_ID, NAME, SITE_CODE)
values (next value for ovv_store_seq,'2059', 'SK', '31321828', 'Zarnovica','24036');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1281', 'PL', '5261037737', 'Glogow','31048');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6266', 'PL', '5261037737', 'Gdansk Obroncow','34123');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6201', 'PL', '5261037737', 'Ruda Slaska Goduli','34141');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6260', 'PL', '5261037737', 'Ciechocinek','34109');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6083', 'PL', '5261037737', 'Mszana Dolna Bus','34257');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6405', 'PL', '5261037737', 'Skarzysko Pilsudskiego','34011');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6058', 'PL', '5261037737', 'Radlin','34046');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1066', 'PL', '5261037737', 'Elk Cake','31117');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1039', 'PL', '5261037737', 'Raciborz Opawska','31132');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6292', 'PL', '5261037737', 'Lodz Natalii','34095');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6117', 'PL', '5261037737', 'Zarow Train','34307');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1050', 'PL', '5261037737', 'Sokolka','31095');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6272', 'PL', '5261037737', 'Gdansk Opolska','34131');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1016', 'PL', '5261037737', 'Gostyn','31063');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6110', 'PL', '5261037737', 'Scinawa','34228');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6253', 'PL', '5261037737', 'Plock Chopina','34098');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6276', 'PL', '5261037737', 'Lodz Dylika','34072');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1291', 'PL', '5261037737', 'Ostroleka','31166');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1023', 'PL', '5261037737', 'Swarzedz Cieszkowskiego','31074');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1202', 'PL', '5261037737', 'Czestochowa','31002');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6040', 'PL', '5261037737', 'Czestochowa Sobieskiego','34043');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6208', 'PL', '5261037737', 'Dabrowa Gornicza Jadwigi','34152');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6177', 'PL', '5261037737', 'Tczew Zwirki','34203');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6295', 'PL', '5261037737', 'Warszawa Wolska','34051');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6004', 'PL', '5261037737', 'Zielona Gora 1k','34006');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6113', 'PL', '5261037737', 'Jozefow','34306');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6139', 'PL', '5261037737', 'Lodz Primus','34049');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6361', 'PL', '5261037737', 'Jaroslaw Pruchnicka','34314');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6275', 'PL', '5261037737', 'Piotrkow Trybunalski','34071');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1011', 'PL', '5261037737', 'Lowicz Kuran','31054');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6406', 'PL', '5261037737', 'Stalowa Wola Okulickiego JM','34013');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1211', 'PL', '5261037737', 'Koszalin','31110');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6009', 'PL', '5261037737', 'Grudziadz Naucz','34030');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1304', 'PL', '5261037737', 'Mosina','34032');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1055', 'PL', '5261037737', 'Warszawa Pine','31131');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1034', 'PL', '5261037737', 'Chelm','31038');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6053', 'PL', '5261037737', 'Miedzyrzec Podlaski','34313');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1234', 'PL', '5261037737', 'Kluczbork','31152');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1065', 'PL', '5261037737', 'Milanowek','31111');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6025', 'PL', '5261037737', 'Czarnkow','34028');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6250', 'PL', '5261037737', 'Nowy Tomysl','34132');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1269', 'PL', '5261037737', 'Bytom Chorzowska','31032');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6039', 'PL', '5261037737', 'Czerwionka','34212');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6034', 'PL', '5261037737', 'Olecko','34040');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1333', 'PL', '5261037737', 'Sucha Beskidzka Handlowa','34279');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6186', 'PL', '5261037737', 'Kamieniec Wroclawski','34248');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6224', 'PL', '5261037737', 'Gliwice Toszecka','34142');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6422', 'PL', '5261037737', 'Tarnowo Podgorne','34354');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6337', 'PL', '5261037737', 'Tarnow','34193');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6165', 'PL', '5261037737', 'Szczecin Mickiewicza','34356');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1295', 'PL', '5261037737', 'Ostrowiec Swietokrzyski','31065');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6096', 'PL', '5261037737', 'Radziejow','34197');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1024', 'PL', '5261037737', 'Lapy Beauty','31120');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1037', 'PL', '5261037737', 'Konstantynow Lodzki','31140');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1237', 'PL', '5261037737', 'Wroclaw Legnicka','31088');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6215', 'PL', '5261037737', 'Swietochlowice','34162');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6268', 'PL', '5261037737', 'Malbork','34125');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6028', 'PL', '5261037737', 'Blonie','34035');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6301', 'PL', '5261037737', 'Warszawa Torunska','34057');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6144', 'PL', '5261037737', 'Zgierz Pieta','34209');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6076', 'PL', '5261037737', 'Kielce Sandomierska','34180');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6042', 'PL', '5261037737', 'Bialobrzegi Koscielna','34322');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6018', 'PL', '5261037737', 'Siedlce Milk','34031');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6283', 'PL', '5261037737', 'Lodz Kopcinskiego','34081');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1224', 'PL', '5261037737', 'Suwalki','31137');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6099', 'PL', '5261037737', 'Makow Podhalanski','34198');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1271', 'PL', '5261037737', 'Poznan Mragowska','31029');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6003', 'PL', '5261037737', 'Szczecin Dunska','34005');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6149', 'PL', '5261037737', 'Zgierz White','34301');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1092', 'PL', '5261037737', 'Trzebnica Connector','31144');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1241', 'PL', '5261037737', 'Starogard Gdanski Zblewska','31092');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6386', 'PL', '5261037737', 'Deblin','34345');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1084', 'PL', '5261037737', 'Bilgoraj','31060');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6249', 'PL', '5261037737', 'Rogozno','34124');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6011', 'PL', '5261037737', 'Rybnik Budowlana','34019');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6400', 'PL', '5261037737', 'Krakow Dobrego Pasterza','34008');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6055', 'PL', '5261037737', 'Makow Mazowiecki','34215');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1060', 'PL', '5261037737', 'Brzeg Dolny','31096');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1069', 'PL', '5261037737', 'Kostrzyn Tory','31114');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6431', 'PL', '5261037737', 'Torun Wapienna','34360');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6187', 'PL', '5261037737', 'Trzemeszno','34305');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1056', 'PL', '5261037737', 'Limanowa','31147');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6180', 'PL', '5261037737', 'Czestochowa Strada','34236');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6054', 'PL', '5261037737', 'Poddebice Factory','34225');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6153', 'PL', '5261037737', 'Zwolen','34223');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6321', 'PL', '5261037737', 'Debica','34188');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1342', 'PL', '5261037737', 'Nowa Ruda','34270');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6234', 'PL', '5261037737', 'Pawlowice','34167');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1078', 'PL', '5261037737', 'Wroclaw Kielczowska','31160');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1330', 'PL', '5261037737', 'Skoczow','34276');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6305', 'PL', '5261037737', 'Piaseczno','34061');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1325', 'PL', '5261037737', 'Andrychow','34260');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1075', 'PL', '5261037737', 'Swiebodzice','31109');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6214', 'PL', '5261037737', 'Katowice Radockiego','34161');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6377', 'PL', '5261037737', 'Bolszewo','34344');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6355', 'PL', '5261037737', 'Klodawa Gorzowska','34295');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1252', 'PL', '5261037737', 'Przemysl Lwowska','31108');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6238', 'PL', '5261037737', 'Kalisz Wojska Polskiego','34073');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6279', 'PL', '5261037737', 'Skierniewice Lelewela','34076');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1222', 'PL', '5261037737', 'Malbork Grobelno','31128');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1215', 'PL', '5261037737', 'Wrzesnia','31125');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6226', 'PL', '5261037737', 'Gliwice Kochanowskiego','34146');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1089', 'PL', '5261037737', 'Konskie','31146');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6032', 'PL', '5261037737', 'Wlodava','34039');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1026', 'PL', '5261037737', 'Miedzyrzecz','31077');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6239', 'PL', '5261037737', 'Kalisz Legionow','34090');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6263', 'PL', '5261037737', 'Dzialdowo','34117');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1212', 'PL', '5261037737', 'Jelenia Gora','31010');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1226', 'PL', '5261037737', 'Ostrow Wielkopolski Ostrovia','31071');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1063', 'PL', '5261037737', 'Grajewo','31130');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1282', 'PL', '5261037737', 'Sochaczew','31164');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1017', 'PL', '5261037737', 'Chrzanow','31062');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6232', 'PL', '5261037737', 'Ziebice','34165');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1255', 'PL', '5261037737', 'Gliwice Helical','31154');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1280', 'PL', '5261037737', 'Inowroclaw WP','31163');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1310', 'PL', '5261037737', 'Bielsko Biala 1k','34002');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6287', 'PL', '5261037737', 'Ozorkow Wyszynskiego','34352');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1273', 'PL', '5261037737', 'Gdynia Kcynska','31025');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6277', 'PL', '5261037737', 'Tomaszow Mazowiecki','34074');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6012', 'PL', '5261037737', 'Kielce Bohaterow','34003');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6178', 'PL', '5261037737', 'Strzyzow','34242');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6026', 'PL', '5261037737', 'Lobez','34027');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6293', 'PL', '5261037737', 'Lodz Przybyszewskiego','34195');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6312', 'PL', '5261037737', 'Radom Sandomierska','34087');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6370', 'PL', '5261037737', 'Karpacz','34258');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1322', 'PL', '5261037737', 'Bielsko Biala Kierowa','34262');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6087', 'PL', '5261037737', 'Slupca','34293');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1054', 'PL', '5261037737', 'Krasnystaw','31085');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6148', 'PL', '5261037737', 'Wloszczowa','34294');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1093', 'PL', '5261037737', 'Koscierzyna Hallera','31149');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1270', 'PL', '5261037737', 'Poznan Opienskego','31028');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1223', 'PL', '5261037737', 'Olawa','31059');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1058', 'PL', '5261037737', 'Gliwice Sosnica','31098');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6037', 'PL', '5261037737', 'Zabno Rondo','34229');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6220', 'PL', '5261037737', 'Knurow 1 Maja','34136');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1042', 'PL', '5261037737', 'Slawno South','31148');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6109', 'PL', '5261037737', 'Swidnica Rainbow','34219');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6089', 'PL', '5261037737', 'Lodz Ladybird','34220');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6143', 'PL', '5261037737', 'Kazimierza Wielka','34303');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6288', 'PL', '5261037737', 'Sulejow','34086');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6176', 'PL', '5261037737', 'Kostrzyn Dworcowa','34246');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1003', 'PL', '5261037737', 'Krapkowice','31047');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1267', 'PL', '5261037737', 'Warszawa Stalowa','31022');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6262', 'PL', '5261037737', 'Golub Dobrzyn','34114');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1264', 'PL', '5261037737', 'Wroclaw Srodmiescie','31031');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6182', 'PL', '5261037737', 'Witkowo','34312');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6252', 'PL', '5261037737', 'Torun Chrobrego','34097');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6242', 'PL', '5261037737', 'Inowroclaw','34105');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1062', 'PL', '5261037737', 'Lubaczow Milk','31119');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6245', 'PL', '5261037737', 'Pila Wyspianskiego','34112');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6284', 'PL', '5261037737', 'Lodz Marysinska','34082');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6211', 'PL', '5261037737', 'Zawiercie','34155');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6020', 'PL', '5261037737', 'Konin Chopina','34026');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6303', 'PL', '5261037737', 'Warszawa Szolc Rogozinskiego','34325');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1233', 'PL', '5261037737', 'Sieradz Republic','31133');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6362', 'PL', '5261037737', 'Kamionki Poznanska','34249');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1276', 'PL', '5261037737', 'Kalisz Majkowska','31043');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6248', 'PL', '5261037737', 'Pleszew Sienkiewicza','34122');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1088', 'PL', '5261037737', 'Plonsk','31145');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1206', 'PL', '5261037737', 'Krakow Kapelanka','31007');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6418', 'PL', '5261037737', 'Mosciska Estrady','34347');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1085', 'PL', '5261037737', 'Zambrow','31126');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'3100', 'PL', '5261037737', 'Krakow Skanska','34700');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1229', 'PL', '5261037737', 'Rzeszow','31012');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6357', 'PL', '5261037737', 'Rawa Mazowiecka','34304');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6311', 'PL', '5261037737', 'Skarzysko Kamienna Spoldz','34080');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6064', 'PL', '5261037737', 'Dobre Miasto','34034');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6218', 'PL', '5261037737', 'Zory','34133');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1334', 'PL', '5261037737', 'Oswiecim Sniadeckiego','34273');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6105', 'PL', '5261037737', 'Lesko','34320');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1002', 'PL', '5261037737', 'Opole','31045');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6101', 'PL', '5261037737', 'Labiszyn','34224');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6194', 'PL', '5261037737', 'Szczecin Policka','34323');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1339', 'PL', '5261037737', 'Walbrzych Glowna','34284');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6329', 'PL', '5261037737', 'Bochnia','34190');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1337', 'PL', '5261037737', 'Opole Lakowa','34271');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6172', 'PL', '5261037737', 'Glogow Jedn Robotniczej','34265');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6319', 'PL', '5261037737', 'Stargard Pilsud','34127');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1013', 'PL', '5261037737', 'Wyszkow','31057');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1018', 'PL', '5261037737', 'Boleslawiec','31072');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6213', 'PL', '5261037737', 'Laziska Gorne','34160');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6084', 'PL', '5261037737', 'Olsztyn Zatorze','34297');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1038', 'PL', '5261037737', 'Dzierzoniow Swidnicka','31099');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6050', 'PL', '5261037737', 'Murowana Goslina','34355');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1231', 'PL', '5261037737', 'Zary','31151');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1059', 'PL', '5261037737', 'Czluchow','31103');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6375', 'PL', '5261037737', 'Chojnice Sukiennikow','34319');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1214', 'PL', '5261037737', 'Wroclaw Marino','31104');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1005', 'PL', '5261037737', 'Myslowice','31049');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1041', 'PL', '5261037737', 'Ostrow Wielkopolski','31097');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6413', 'PL', '5261037737', 'Krakow Cinema','34340');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1035', 'PL', '5261037737', 'Kedzierzyn Kozle','31129');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6015', 'PL', '5261037737', 'Zdzieszowice','34033');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6330', 'PL', '5261037737', 'Bialystok 27 Lipca','34175');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6206', 'PL', '5261037737', 'Czestochowa Relax','34149');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1261', 'PL', '5261037737', 'Warszawa Piastow','31035');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1072', 'PL', '5261037737', 'Sosnowiec 1 Maja','31112');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6354', 'PL', '5261037737', 'Jastrowie','34318');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1238', 'PL', '5261037737', 'Pila','31102');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6316', 'PL', '5261037737', 'Szczecin Rydla','34116');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6426', 'PL', '5261037737', 'Nowy Targ','34357');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1277', 'PL', '5261037737', 'Wloclawek','31036');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6008', 'PL', '5261037737', 'Przemysl Boruty','34021');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1249', 'PL', '5261037737', 'Tarnowskie Gory','31041');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1028', 'PL', '5261037737', 'Szczecinek Field','31084');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1275', 'PL', '5261037737', 'Mielec','31044');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6302', 'PL', '5261037737', 'Warszawa DT Wola','34058');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1213', 'PL', '5261037737', 'Gorzow Wielkopolski','31017');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1251', 'PL', '5261037737', 'Belchatow Echo','31153');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1239', 'PL', '5261037737', 'Pulawy Orange','31121');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1096', 'PL', '5261037737', 'Zory Malinowa','31162');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1070', 'PL', '5261037737', 'Leczna Bingo','31116');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6408', 'PL', '5261037737', 'Zakopane Chramcowski','34016');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1268', 'PL', '5261037737', 'Warszawa KEN','31023');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6212', 'PL', '5261037737', 'Bytom Tarnogorska','34156');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1045', 'PL', '5261037737', 'Rydultowy','31100');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1293', 'PL', '5261037737', 'Rybnik','31064');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1272', 'PL', '5261037737', 'Gorzow Gorczynska','31027');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6114', 'PL', '5261037737', 'Goldap Kino','34254');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6189', 'PL', '5261037737', 'Skierniewice Lambert','34227');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1256', 'PL', '5261037737', 'Lomza Echo','31155');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6290', 'PL', '5261037737', 'Aleksandrow Lodzki','34093');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1220', 'PL', '5261037737', 'Turek','31089');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1244', 'PL', '5261037737', 'Kielce','31016');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6335', 'PL', '5261037737', 'Tuchow','34192');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1007', 'PL', '5261037737', 'Bytom Szombierki','31052');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6030', 'PL', '5261037737', 'Terespol','34216');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1328', 'PL', '5261037737', 'Zywiec Dworcowa','34286');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1091', 'PL', '5261037737', 'Swietochlowice Echo','31142');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1009', 'PL', '5261037737', 'Zywiec','31053');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1297', 'PL', '5261037737', 'Gdansk Chelm','31091');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1031', 'PL', '5261037737', 'Rawicz','31081');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1348', 'PL', '5261037737', 'Glogow Budowlanych','34264');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6328', 'PL', '5261037737', 'Sokolow Podlaski','34172');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1077', 'PL', '5261037737', 'Czechowice Dziedzice','31165');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6345', 'PL', '5261037737', 'Lubaczow','34170');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1014', 'PL', '5261037737', 'Ketrzyn Mazowiecka','31159');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6147', 'PL', '5261037737', 'Zychlin Zeromskiego','34233');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1012', 'PL', '5261037737', 'Namyslow','31055');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6296', 'PL', '5261037737', 'Warszawa Goclaw','34052');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1219', 'PL', '5261037737', 'Opole Ozimska','31094');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6048', 'PL', '5261037737', 'Wabrzezno','34206');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1227', 'PL', '5261037737', 'Grojec','31134');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6403', 'PL', '5261037737', 'Przemysl Zeromskiego','34015');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6090', 'PL', '5261037737', 'Lodz Spar','34289');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1303', 'PL', '5261037737', 'Warszawa Fieldorfa','34017');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6098', 'PL', '5261037737', 'Losice','34245');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6072', 'PL', '5261037737', 'Ledziny','34210');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1008', 'PL', '5261037737', 'Jawor','31051');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6065', 'PL', '5261037737', 'Gostynin','34207');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1216', 'PL', '5261037737', 'Bielsko Biala','31013');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1022', 'PL', '5261037737', 'Swiebodzin','31082');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1208', 'PL', '5261037737', 'Warszawa Mory','31005');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6300', 'PL', '5261037737', 'Minsk Mazowiecki','34056');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6086', 'PL', '5261037737', 'Chojnice Biedronka','34288');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1274', 'PL', '5261037737', 'Szczecin','31024');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6267', 'PL', '5261037737', 'Nidzica','34126');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1283', 'PL', '5261037737', 'Starachowice','31167');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1205', 'PL', '5261037737', 'Poznan Serbska','31006');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1324', 'PL', '5261037737', 'Kety','34266');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1020', 'PL', '5261037737', 'Swiecie','31046');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6174', 'PL', '5261037737', 'Gora','34185');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6255', 'PL', '5261037737', 'Torun Reja','34100');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6244', 'PL', '5261037737', 'Chodziez Ujska','34110');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1254', 'PL', '5261037737', 'Stargard','31020');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1094', 'PL', '5261037737', 'Dabrowa Tarnowska','31156');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6289', 'PL', '5261037737', 'Lodz Elsnera','34088');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6120', 'PL', '5261037737', 'Opole Lubelskie','34221');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1004', 'PL', '5261037737', 'Kozienice','31068');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1086', 'PL', '5261037737', 'Jelenia Gora Orlen','31069');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1010', 'PL', '5261037737', 'Lubartow','31107');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1051', 'PL', '5261037737', 'Nisko Lucky','31138');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6085', 'PL', '5261037737', 'Biskupiec','34290');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6134', 'PL', '5261037737', 'Szydlowiec PW','34255');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6116', 'PL', '5261037737', 'Mlawa Sienkiewicza','34317');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6179', 'PL', '5261037737', 'Poreba','34302');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1340', 'PL', '5261037737', 'Walbrzych Kasztelanska','34285');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6022', 'PL', '5261037737', 'Zlotow','34029');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6155', 'PL', '5261037737', 'Barcin','34201');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6265', 'PL', '5261037737', 'Chelmza','34119');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1074', 'PL', '5261037737', 'Staszow Stone','31122');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6091', 'PL', '5261037737', 'Radzymin Rex','34292');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6146', 'PL', '5261037737', 'Dzierzgon','34309');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1043', 'PL', '5261037737', 'Sulechow','31124');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6036', 'PL', '5261037737', 'Skoczow Fabryczna','34300');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1201', 'PL', '5261037737', 'Wroclaw Ikea','31001');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6306', 'PL', '5261037737', 'Garwolin','34062');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1052', 'PL', '5261037737', 'Kolbuszowa','31143');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1243', 'PL', '5261037737', 'Radzyn Podlaski Lotos','31113');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6229', 'PL', '5261037737', 'Strzelin','34158');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6356', 'PL', '5261037737', 'Klaj','34315');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1246', 'PL', '5261037737', 'Leszno','31139');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6230', 'PL', '5261037737', 'Pyskowice','34159');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6209', 'PL', '5261037737', 'Siemianowice Niepodleglosci','34153');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6041', 'PL', '5261037737', 'Parczew','34042');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6498', 'PL', '5261037737', 'Muszyna','34348');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6181', 'PL', '5261037737', 'Warszawa Patriotow','34240');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1331', 'PL', '5261037737', 'Ustron Cieszynska','34281');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6097', 'PL', '5261037737', 'Choszczno Ahold','34196');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6257', 'PL', '5261037737', 'Aleksandrow Kujawski','34104');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6233', 'PL', '5261037737', 'Rybnik Boguszowice','34166');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6399', 'PL', '5261037737', 'Krakow Babinskiego','34253');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6088', 'PL', '5261037737', 'Lubawa','34299');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1019', 'PL', '5261037737', 'Szamotuly','31067');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6207', 'PL', '5261037737', 'Tarnowskie Gory Sojki','34150');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1265', 'PL', '5261037737', 'Krakow Prokocim','31033');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1209', 'PL', '5261037737', 'Tychy','31009');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1029', 'PL', '5261037737', 'Gubin Donald','31080');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6173', 'PL', '5261037737', 'Miechow Raclawicka','34241');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6078', 'PL', '5261037737', 'Jedrzejow Partyzantow','34181');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6014', 'PL', '5261037737', 'Ruda Slaska Gornoslaska','34018');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6428', 'PL', '5261037737', 'Piwniczna','34359');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6127', 'PL', '5261037737', 'Chrzastowice Opolska','34349');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6057', 'PL', '5261037737', 'Nasielsk','34036');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6031', 'PL', '5261037737', 'Strykow Golden Lion','34218');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1210', 'PL', '5261037737', 'Lodz Baluty','31008');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6231', 'PL', '5261037737', 'Rybnik Dworek','34163');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6259', 'PL', '5261037737', 'Torun Merkury','34108');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1221', 'PL', '5261037737', 'Zielona Gora','31011');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1071', 'PL', '5261037737', 'Hrubieszow Visavis','31115');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6363', 'PL', '5261037737', 'Marki','34353');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6073', 'PL', '5261037737', 'Skawina','34211');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6071', 'PL', '5261037737', 'Pinczow','34135');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1247', 'PL', '5261037737', 'Gdynia II','31040');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6017', 'PL', '5261037737', 'Zabrze Freedom','34025');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6066', 'PL', '5261037737', 'Nowe Miasto Lubawskie','34214');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6304', 'PL', '5261037737', 'Wolomin','34060');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1309', 'PL', '5261037737', 'Myslenice Savia','34244');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6038', 'PL', '5261037737', 'Kielce Sawicka','34045');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6108', 'PL', '5261037737', 'Lebork Legionow','34202');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6210', 'PL', '5261037737', 'Chorzow Graniczna','34154');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6333', 'PL', '5261037737', 'Nowy Dwor Gdanski','34184');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6007', 'PL', '5261037737', 'Orzesze','34022');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6047', 'PL', '5261037737', 'Komorniki','34226');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1306', 'PL', '5261037737', 'Zabierzow','34047');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6323', 'PL', '5261037737', 'Brzozow','34189');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1203', 'PL', '5261037737', 'Gliwice','31004');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1082', 'PL', '5261037737', 'Brzeszcze Cross','31135');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6320', 'PL', '5261037737', 'Bialystok Bialostoczek','34067');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6407', 'PL', '5261037737', 'Tarnow Starodabrowska','34014');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6079', 'PL', '5261037737', 'Leczna','34174');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1327', 'PL', '5261037737', 'Wadowice Fatimskiej','34282');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1242', 'PL', '5261037737', 'Nowy Sacz','31127');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1262', 'PL', '5261037737', 'Olsztyn','31026');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1263', 'PL', '5261037737', 'Kalisz WP','31030');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6285', 'PL', '5261037737', 'Lodz Retkinska','34083');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6322', 'PL', '5261037737', 'Sroda Wlkp','34182');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1245', 'PL', '5261037737', 'Warszawa Cranberries','31056');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6082', 'PL', '5261037737', 'Znin','34256');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1230', 'PL', '5261037737', 'Tarnow Gemini','31123');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6121', 'PL', '5261037737', 'Odolanow','34350');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1006', 'PL', '5261037737', 'Lukow','31050');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1260', 'PL', '5261037737', 'Lodz Srodmiescie','31034');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1336', 'PL', '5261037737', 'Oswiecim Majzla','34274');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1044', 'PL', '5261037737', 'Garwolin Kosciuszki','31101');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1329', 'PL', '5261037737', 'Zywiec Kosciuszki','34287');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6235', 'PL', '5261037737', 'Pszczyna','34168');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6271', 'PL', '5261037737', 'Rumia','34130');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1033', 'PL', '5261037737', 'Knurow','31083');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6035', 'PL', '5261037737', 'Grudziadz Poniatowskiego','34213');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1204', 'PL', '5261037737', 'Lodz Widzew','31003');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1032', 'PL', '5261037737', 'Bogatynia Syndyk','31078');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6219', 'PL', '5261037737', 'Bielsko Biala Stawowa','34134');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6401', 'PL', '5261037737', 'Krakow Wybickiego','34010');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6278', 'PL', '5261037737', 'Pabianice','34075');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1076', 'PL', '5261037737', 'Gora Kalwaria','31161');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1067', 'PL', '5261037737', 'Ustron','31106');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6152', 'PL', '5261037737', 'Lipsko','34222');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1207', 'PL', '5261037737', 'Bydgoszcz','31015');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1030', 'PL', '5261037737', 'Wodzislaw','31066');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6308', 'PL', '5261037737', 'Warszawa Czerska','34064');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6200', 'PL', '5261037737', 'Tychy Edukacji','34139');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6137', 'PL', '5261037737', 'Wschowa','34311');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6000', 'PL', '5261037737', 'Chorzow Batorego','34001');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1064', 'PL', '5261037737', 'Tuszyn Cross','31136');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6280', 'PL', '5261037737', 'Lodz Wlokniarzy','34077');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1046', 'PL', '5261037737', 'Naklo','31105');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1021', 'PL', '5261037737', 'Zagan','31075');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6391', 'PL', '5261037737', 'Krasnik Urzedowska','34351');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1236', 'PL', '5261037737', 'Ruda Slaska','31019');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6258', 'PL', '5261037737', 'Chelmno','34107');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6104', 'PL', '5261037737', 'Niepolomice Up Market','34321');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6274', 'PL', '5261037737', 'Lodz Zgierska','34070');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6358', 'PL', '5261037737', 'Lacko','34310');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1105', 'PL', '5261037737', 'Ropczyce','31058');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6309', 'PL', '5261037737', 'Pultusk','34065');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6150', 'PL', '5261037737', 'Jedlicze','34217');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1225', 'PL', '5261037737', 'Bochnia Visa','31090');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6317', 'PL', '5261037737', 'Szczecin Jodlowa','34120');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1326', 'PL', '5261037737', 'Wadowice Westerplatte','34283');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6001', 'PL', '5261037737', 'Grudziadz','34004');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6045', 'PL', '5261037737', 'Mikolow Centre','34208');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6205', 'PL', '5261037737', 'Katowice Tysiaclecia','34148');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1305', 'PL', '5261037737', 'Piotrkow Trybunalski Focus','34048');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6049', 'PL', '5261037737', 'Kolbudy','34339');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1027', 'PL', '5261037737', 'Polkowice','31076');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1287', 'PL', '5261037737', 'Swidnica','31037');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1228', 'PL', '5261037737', 'Lublin','31018');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6118', 'PL', '5261037737', 'Dobrzen Wielki','34259');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1250', 'PL', '5261037737', 'Walbrzych','31039');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6416', 'PL', '5261037737', 'Kowale','34342');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1307', 'PL', '5261037737', 'Wloclawek Urzad','34199');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1068', 'PL', '5261037737', 'Pruszcz Gdanski','31118');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6010', 'PL', '5261037737', 'Poznan Luzycka','34020');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1061', 'PL', '5261037737', 'Legnica Ferio','31093');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6310', 'PL', '5261037737', 'Raszyn','34066');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6128', 'PL', '5261037737', 'Suchedniow','34308');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6221', 'PL', '5261037737', 'Jastrzebie Zdroj','34137');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1025', 'PL', '5261037737', 'Koscian','31079');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1235', 'PL', '5261037737', 'Lubin Ignacego','31014');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6081', 'PL', '5261037737', 'Czarna Bialostocka','34298');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6043', 'PL', '5261037737', 'Jaktorow','34291');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6044', 'PL', '5261037737', 'Ryki','34232');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1266', 'PL', '5261037737', 'Warszawa Gorczewska','31021');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6227', 'PL', '5261037737', 'Wola Brzeszcze','34151');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6223', 'PL', '5261037737', 'Bielsko Biala Grunwaldzka','34140');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1248', 'PL', '5261037737', 'Stalowa Wola','31073');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6282', 'PL', '5261037737', 'Lodz Wyszynskiego','34079');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1217', 'PL', '5261037737', 'Katowice AGIP','31070');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6016', 'PL', '5261037737', 'Sulejowek','34038');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6315', 'PL', '5261037737', 'Starachowice Radomska','34092');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6175', 'PL', '5261037737', 'Warszawa Kleszczowa','34296');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1335', 'PL', '5261037737', 'Oswiecim Nojego','34275');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1338', 'PL', '5261037737', 'Opole Roweckiego','34272');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6365', 'PL', '5261037737', 'Warszawa Cyrulikow','34324');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6299', 'PL', '5261037737', 'Warszawa Tarchomin','34055');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'1259', 'PL', '5261037737', 'Skierniewice','31042');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6286', 'PL', '5261037737', 'Zgierz','34084');
insert into OVV_STORE (id, cost_centre, COUNTRY_CODE, PARTNER_ID, name, site_code)
values (next value for ovv_store_seq,'6023', 'PL', '5261037737', 'Bielsko Biala Babiogorska','34023');
