site.name=Online Voucher Validation - Batch Processing for Tesco
czEdenredBatchJob.description = Create a batch file for <b>Edenred CZ</b> issuer and upload it to a remote SFTP server
czEdenredRecoJob.description = Reconciliate a <b>Edenred CZ</b> response file with our database
czLchdBatchJob.description = Create a batch file for <b>LCHD CZ</b> issuer and upload it to a remote SFTP server
czLchdRecoJob.description = Reconciliate a <b>LCHD CZ</b> response file with our database
czSodexoBatchJob.description = Create a batch file for <b>Sodexo CZ</b> issuer and upload it to a remote SFTP server
czSodexoBatchJobPilotDaily.description = Create a pilot batch file for <b>Sodexo CZ</b> issuer (DAILY period) and upload it to a remote SFTP server
czSodexoBatchJobPilotWeekly.description = Create a pilot batch file for <b>Sodexo CZ</b> issuer (WEEKLY period) and upload it to a remote SFTP server
czSodexoRecoJob.description = Reconciliate a <b>Sodexo CZ</b> response file with our database
czTvmBatchJob.description = Create a batch file for <b>TVM CZ</b> issuer and upload it to a remote SFTP server
czTvmRecoJob.description = Reconciliate a <b>TVM CZ</b> response file with our database
huEdenredBatchJob.description = Create a batch file for <b>Edenred HU</b> issuer and upload it to a remote SFTP server
huEdenredRecoJob.description = Reconciliate a <b>Edenred HU</b> response file with our database
huTvmBatchJob.description = Create a batch file for <b>TVM HU</b> issuer and upload it to a remote SFTP server
huTvmRecoJob.description = Reconciliate a <b>TVM HU</b> response file with our database
plLchdBatchJob.description = Create a batch file for <b>LCHD PL</b> issuer and upload it to a remote SFTP server
plLchdRecoJob.description = Reconciliate a <b>LCHD PL</b> response file with our database
plSodexoBatchJob.description = Create a batch file for <b>Sodexo PL</b> issuer and upload it to a remote SFTP server
plSodexoRecoJob.description = Reconciliate a <b>Sodexo PL</b> response file with our database
plTvmBatchJob.description = Create a batch file for <b>TVM PL</b> issuer and upload it to a remote SFTP server
plTvmRecoJob.description = Reconciliate a <b>TVM PL</b> response file with our database
plWasaBatchJob.description = Create a batch file for <b>Wasa PL</b> issuer and upload it to a remote SFTP server
plWasaRecoJob.description = Reconciliate a <b>Wasa PL</b> response file with our database
skDoxxBatchJob.description = Create a batch file for <b>Doxx SK</b> issuer and upload it to a remote SFTP server
skDoxxRecoJob.description = Reconciliate a <b>Doxx SK</b> response file with our database
skEdenredBatchJob.description = Deprecated job configuration, do not use it.
skEdenredGiftBatchJob.description = Create a batch file for <b>Edenred Gift SK</b> issuer and upload it to a remote SFTP server
skEdenredMealBatchJob.description = Create a batch file for <b>Edenred Meal SK</b> issuer and upload it to a remote SFTP server
skEdenredRecoJob.description = Reconciliate a <b>Edenred SK</b> response file with our database
skLchdBatchJob.description = Create a batch file for <b>LCHD SK</b> issuer and upload it to a remote SFTP server
skLchdRecoJob.description = Reconciliate a <b>LCHD SK</b> response file with our database
skTvmBatchJob.description = Create a batch file for <b>TVM SK</b> issuer and upload it to a remote SFTP server
skTvmRecoJob.description = Reconciliate a <b>TVM SK</b> response file with our database
skWasaBatchJob.description = Create a batch file for <b>Wasa SK</b> issuer and upload it to a remote SFTP server
skWasaRecoJob.description = Reconciliate a <b>Wasa SK</b> response file with our database
company.url=http://www.tesco.com
company.name=Tesco
product.url=
product.name=OVV Batch
