<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:p="http://www.springframework.org/schema/p" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:context="http://www.springframework.org/schema/context"
    xmlns:batch="http://www.springframework.org/schema/batch" xmlns:int="http://www.springframework.org/schema/integration"
    xmlns:task="http://www.springframework.org/schema/task" xmlns:int-sftp="http://www.springframework.org/schema/integration/sftp"
    xsi:schemaLocation="http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch.xsd
        http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
        http://www.springframework.org/schema/integration/sftp http://www.springframework.org/schema/integration/sftp/spring-integration-sftp.xsd">

    <!-- !!!! This file contains common bean definitions to all batch jobs !!!! -->

    <!-- Step is responsible for setting batch output file name -->
    <batch:step id="setupOutputFileStep">
        <batch:tasklet ref="ovvOutputFileGenerator" />
    </batch:step>

    <!-- Step is responsible for setting interval where a current batch will
        be generated -->
    <batch:step id="setupBatchIntervalStep">
        <batch:tasklet ref="ovvBatchIntervalGenerator" />
    </batch:step>

    <!-- Definition of a read-write step -->
    <batch:step id="readWriteStep">
        <batch:tasklet>
            <batch:chunk reader="itemReader" writer="compositeWriter"
                commit-interval="#{jobParameters['pageSize']}">
                <batch:streams>
                    <batch:stream ref="csvFileItemWriter" />
                    <batch:stream ref="reportBatchItemWriter" />
                </batch:streams>
            </batch:chunk>
            <batch:listeners>
                <batch:listener ref="reportBatchItemWriter" />
            </batch:listeners>
        </batch:tasklet>
    </batch:step>

    <!-- Composite writer definition, i.e. this composite writer writes into
        two resources (DB and CSV file) -->
    <bean id="compositeWriter"
        class="org.springframework.batch.item.support.CompositeItemWriter">
        <property name="delegates">
            <list>
                <ref bean="csvFileItemWriter" />
                <ref bean="reportBatchItemWriter" />
            </list>
        </property>
    </bean>

    <!-- This step is responsible for adding a number of all vouchers in a current
        file to a file header -->
    <batch:step id="addItemNumberStep">
        <batch:tasklet ref="batchFileHeaderTasklet" />
    </batch:step>

    <!-- This step is responsible for compressing of a batch file -->
    <batch:step id="compressBatchFileStep">
        <batch:tasklet ref="batchFileCompressTasklet" />
    </batch:step>

    <!-- This step is responsible for uploading a batch file to a remote SFTP
        server () -->
    <batch:step id="uploadToSftpStep">
        <batch:tasklet ref="sftpUploadTasklet" />
    </batch:step>

        <!-- This step is responsible for loading OVV Stores from DB -->
    <batch:step id="loadOvvStoresStep">
        <batch:tasklet ref="loadOvvStoresTasklet" />
    </batch:step>

    <bean id="batchFileCompressTasklet" parent="dummyTasklet"/>
    <bean id="batchFileHeaderTasklet" class="cz.wincor.ovv.batch.tasklet.BatchFileHeaderTasklet" />
    <bean id="sftpUploadTasklet" parent="dummyTasklet"/>

    <!-- Definition of a CSV file writer, i.e. writer which creates a main batch
        file -->
    <bean id="csvFileItemWriter" class="org.springframework.batch.item.file.FlatFileItemWriter"
        scope="step">
        <!-- write to this csv file -->
        <property name="resource"
            value="file://#{jobExecutionContext['reportFile'].filePath}" />
        <property name="shouldDeleteIfExists" value="true" />
        <property name="lineAggregator">
            <bean
                class="org.springframework.batch.item.file.transform.DelimitedLineAggregator">
                <property name="delimiter" value="#{jobParameters['output.file.delimiter']}" />
                <property name="fieldExtractor">
                    <bean
                        class="org.springframework.batch.item.file.transform.BeanWrapperFieldExtractor">
                        <property name="names" value="#{jobParameters['output.file.columns']}" />
                    </bean>
                </property>
            </bean>
        </property>
    </bean>

    <!-- Definition of a JDBC item reader, i.e. reader which reads data from
        transaction log -->
    <bean id="itemReader"
        class="org.springframework.batch.item.database.JdbcPagingItemReader"
        scope="step">
        <property name="dataSource" ref="ovvDataSource" />
        <property name="queryProvider">
            <bean
                class="org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean">
                <property name="dataSource" ref="ovvDataSource" />
                <property name="selectClause"
                    value="select req.id as requestId, v.id as voucherId, req.device_id as deviceId, req.device_local_date_time as deviceLocalDateTime, req.server_utc_date_time as serverUtcDateTime, v.server_local_date_time as serverLocalDateTime, req.amount as amount, req.pos_currency_code as currencyCode, req.partner_id as partnerId, req.payment_place as paymentPlace, req.stan as stan, req.transaction_type as transactionType, v.voucher_number as voucherNumber, req.country_code as countryCode" />
                <property name="fromClause"
                    value="from VOUCHER v join OVV_TRANSACTION_REQUEST req on (v.related_transaction_request_id = req.id)" />
                <property name="whereClause"
                    value="where req.country_code = :countryCode and req.partner_id in (:partnerId) and v.voucher_state = 'USED' and req.ovv_issuer = :ovvIssuer and v.server_local_date_time between :startDate and :endDate" />
                <property name="sortKey" value="voucherId" />
            </bean>
        </property>
        <property name="parameterValues">
            <map>
                <entry key="startDate" value="#{jobExecutionContext['reader.startDateTime']}" />
                <entry key="endDate" value="#{jobExecutionContext['reader.endDateTime']}" />
                <entry key="ovvIssuer" value="#{jobParameters['ovvIssuer']}" />
                <entry key="partnerId" value="#{jobExecutionContext['partnerId']}" />
                <entry key="countryCode" value="#{jobParameters['output.file.name.country']}" />
            </map>
        </property>
        <property name="pageSize" value="#{jobParameters['pageSize']}" />
        <property name="rowMapper">
            <bean class="cz.wincor.ovv.batch.support.VoucherRowMapper">
                <property name="countryCode" value="#{jobParameters['output.file.name.country']}"/>
                <property name="receiverTimezone" value="#{jobParameters['receiverTimezone']}"/>
                <property name="storeManager" value="#{jobExecutionContext['ovvStoreManager']}"/>
            </bean>
        </property>
    </bean>

    <bean id="ovvBatchParameterValidator"
        class="cz.wincor.ovv.batch.validator.JobParameterValidator">
        <property name="requiredParameters">
            <set>
                <value>output.file.columns</value>
                <value>output.file.delimiter</value>
                <value>output.file.name.country</value>
                <value>output.file.path</value>
                <value>ovvIssuer</value>
                <value>partnerId</value>
                <value>pageSize</value>
                <value>reportStartDate</value>
                <value>receiverTimezone</value>
            </set>
        </property>
    </bean>

    <bean id="scheduledExecutorService"
          class="java.util.concurrent.Executors" factory-method="newScheduledThreadPool">
        <constructor-arg value="1"/>
    </bean>

    <bean id="jobListener" class="cz.wincor.ovv.batch.listener.CustomJobListener" scope="job">
        <property name="jobService" ref="jobService" />
        <property name="scheduler" ref="scheduledExecutorService" />
        <property name="rescheduleEnabled" value="${batch.job.failed.reschedule.enabled:true}" />
        <property name="maxRepeatCount" value="${batch.job.failed.reschedule.maxRepeatCount:5}" />
        <property name="delays" value="${batch.job.failed.reschedule.delays}" />
    </bean>

    <bean id="loggingMdcListener" class="cz.wincor.ovv.batch.listener.LogbackJobExecutionListener" scope="job"/>

</beans>
