<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:p="http://www.springframework.org/schema/p" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:context="http://www.springframework.org/schema/context"
    xmlns:batch="http://www.springframework.org/schema/batch" xmlns:int="http://www.springframework.org/schema/integration"
    xmlns:task="http://www.springframework.org/schema/task" xmlns:int-sftp="http://www.springframework.org/schema/integration/sftp"
    xsi:schemaLocation="http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch.xsd
        http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd
        http://www.springframework.org/schema/integration/sftp http://www.springframework.org/schema/integration/sftp/spring-integration-sftp.xsd">

    <!-- !!!! This file contains common bean definitions to all reconciliation
        jobs !!!! -->

    <!-- This step is responsible for searching for a related batch file -->
    <batch:step id="findRelatedBatchFileStep">
        <batch:tasklet ref="reportFileFinderTasklet" />
    </batch:step>

    <!-- This step is responsible for reading data from CSV reco file and merging
        them with a DB -->
    <batch:step id="readWriteRecoStep">
        <batch:tasklet task-executor="taskExecutor">
            <batch:chunk reader="flatFileItemReader" writer="recoReportBatchItemWriter"
                commit-interval="1000" />
        </batch:tasklet>
        <batch:listeners>
            <batch:listener ref="recoReportBatchItemWriter" />
        </batch:listeners>
    </batch:step>

    <!-- This step is responsible for extracting incoming files (if needed) -->
    <batch:step id="recoFileExtractStep">
        <batch:tasklet ref="recoFileExtractTasklet" />
    </batch:step>

    <!-- This step is responsible for archiving already processed files -->
    <batch:step id="fileArchiveStep">
        <batch:tasklet ref="fileArchiveTasklet" />
    </batch:step>

    <!-- tasklet responsible for archiving request files -->
    <bean id="fileArchiveTasklet" class="cz.wincor.ovv.batch.tasklet.FileArchiveTasklet"
        scope="step">
        <property name="archiveDirectory" value="#{jobParameters['output.path']}" />
        <property name="filesToArchive">
            <set>
                <value>#{jobExecutionContext['input.file.name']}</value>
                <value>#{jobParameters['input.file.name']}</value>
            </set>
        </property>
    </bean>

    <!-- The definition of a reader (reads data from an incoming RECO CSV file) -->
    <bean id="flatFileItemReader"
        class="cz.wincor.ovv.batch.reader.SynchronizedFlatFileItemReader"
        scope="step">
        <property name="resource"
            value="file:///#{jobExecutionContext['input.file.name']}" />
        <property name="encoding" value="UTF-8" />
        <property name="linesToSkip" value="#{jobParameters['input.file.linesToSkip']}" />
        <property name="lineMapper">
            <bean class="org.springframework.batch.item.file.mapping.DefaultLineMapper">
                <property name="lineTokenizer">
                    <bean
                        class="org.springframework.batch.item.file.transform.DelimitedLineTokenizer">
                        <property name="names" value="#{jobParameters['input.file.columns']}" />
                        <property name="delimiter" value="#{jobParameters['input.file.delimiter']}" />
                        <property name="strict" value="false" />
                    </bean>
                </property>
                <property name="fieldSetMapper">
                    <bean class="cz.wincor.ovv.batch.support.ReportFieldSetMapper" />
                </property>
            </bean>
        </property>
    </bean>

    <!-- Definition of a task executor for parallel processing used when processing
        incoming RECO file -->
    <bean id="taskExecutor" class="org.springframework.core.task.SimpleAsyncTaskExecutor">
        <property name="concurrencyLimit"
            value="${batch.async.availableProcessors:#{T(java.lang.Runtime).getRuntime().availableProcessors()}}"></property>
    </bean>

     <bean id="ovvRecoParameterValidator"
        class="cz.wincor.ovv.batch.validator.JobParameterValidator">
        <property name="requiredParameters">
            <set>
                <value>input.file.columns</value>
                <value>input.file.delimiter</value>
                <value>input.file.linesToSkip</value>
                <value>failOnMissingVoucher</value>
                <value>output.path</value>
            </set>
        </property>
    </bean>

</beans>
