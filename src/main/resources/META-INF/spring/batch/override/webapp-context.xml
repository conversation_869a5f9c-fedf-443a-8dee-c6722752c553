<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
    xmlns:jdbc="http://www.springframework.org/schema/jdbc" xmlns:util="http://www.springframework.org/schema/util"
    xmlns:batch="http://www.springframework.org/schema/batch"
    xsi:schemaLocation="http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch.xsd
        http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc.xsd
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <context:component-scan base-package="cz.wincor.ovv.batch" />
    <context:annotation-config />

    <bean id="placeholderPropertiesParent"
        class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer"
        abstract="true">
        <property name="locations">
            <list>
                <value>classpath:/org/springframework/batch/admin/bootstrap/batch.properties
                </value>
                <value>classpath:/conf/*.properties</value>
                <value>${CONFIGURATION_PATH}/*.properties</value>
            </list>
        </property>
        <property name="ignoreUnresolvablePlaceholders" value="true" />
        <property name="ignoreResourceNotFound" value="true" />
        <property name="localOverride" value="false" />
        <property name="order" value="0" />
    </bean>

<!--     setup default value for ovv-batch base directory -->
<!--     <bean id="baseDirectory" -->
<!--         class="org.springframework.batch.support.SystemPropertyInitializer"> -->
<!--         <property name="keyName" value="ovv.batch.base.directory" /> -->
<!--         <property name="defaultValue" ref="tempDirectory" /> -->
<!--     </bean> -->

<!--     Get temp dir -->
<!--     <bean id="tempDirectory" class="java.lang.System" factory-method="getProperty"> -->
<!--         <constructor-arg value="java.io.tmpdir" /> -->
<!--     </bean> -->

    <bean id="placeholderProperties" parent="placeholderPropertiesParent" />

    <bean id="incrementer"
        class="cz.wincor.ovv.batch.support.TrivialJobParametersIncrementer" />


    <bean id="messageSource"
        class="org.springframework.context.support.ResourceBundleMessageSource">
        <property name="basename" value="messages" />
    </bean>

</beans>
