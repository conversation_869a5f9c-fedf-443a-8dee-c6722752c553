<beans:beans xmlns="http://www.springframework.org/schema/security"
    xmlns:beans="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    http://www.springframework.org/schema/security http://www.springframework.org/schema/security/spring-security.xsd">

    <http auto-config="true" use-expressions="true">
        <intercept-url pattern="/login" access="permitAll" />
        <intercept-url pattern="/resources/**" access="permitAll" />
        <intercept-url pattern="/jobs/**/*.json" access="permitAll" />
        <intercept-url pattern="/jobs.json" access="permitAll" />
        <intercept-url pattern="/**/j_spring_security_logout" access="permitAll" />
        <intercept-url pattern="/" access="hasRole('ROLE_ADMIN')" />
        <intercept-url pattern="/**" access="hasRole('ROLE_ADMIN')" />
        <form-login login-page="/login" default-target-url="/jobs?pageSize=100" always-use-default-target="true"
                    login-processing-url="/j_spring_security_check" authentication-failure-url="/login?error"
                    username-parameter="username" password-parameter="password" />
        <logout logout-url="/j_spring_security_logout" logout-success-url="/login?logout" delete-cookies="JSESSIONID"
                invalidate-session="true" />
    </http>



    <authentication-manager>
        <authentication-provider ref="radiusAuthenticationProvider" />
        <authentication-provider>
            <password-encoder ref="encoder" />
            <!-- Select users and user_roles from database -->
            <jdbc-user-service data-source-ref="batchDataSource"
                users-by-username-query="select username, password, enabled from batch_user where username = ?"
                authorities-by-username-query="select username, role from user_roles where username = ?" />
        </authentication-provider>

    </authentication-manager>

    <beans:bean id="encoder"
        class="org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder">
        <beans:constructor-arg name="strength" value="10" />
    </beans:bean>

    <beans:bean id="mvcHandlerMappingIntrospector" class="org.springframework.web.servlet.handler.HandlerMappingIntrospector" />
    <beans:bean id="webExpressionHandler" class="org.springframework.security.web.access.expression.DefaultWebSecurityExpressionHandler" />

</beans:beans>
