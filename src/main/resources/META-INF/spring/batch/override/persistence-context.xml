<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jpa="http://www.springframework.org/schema/data/jpa"
    xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
    xmlns:p="http://www.springframework.org/schema/p"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans.xsd
    http://www.springframework.org/schema/data/jpa
    http://www.springframework.org/schema/data/jpa/spring-jpa.xsd
    http://www.springframework.org/schema/tx
    http://www.springframework.org/schema/tx/spring-tx.xsd
    http://www.springframework.org/schema/context
    http://www.springframework.org/schema/context/spring-context.xsd">


    <!-- Create default configuration for Hibernate -->
    <bean id="hibernateJpaVendorAdapter"
        class="org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter">
        <property name="showSql" value="${batch.database.showSql:false}" />
        <property name="database" value="${batch.database.vendor:ORACLE}" />
    </bean>

    <!-- Configure the entity manager factory bean -->
    <bean id="batchEntityManagerFactory"
        class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean">
        <property name="dataSource" ref="batchDataSource" />
        <property name="jpaVendorAdapter" ref="hibernateJpaVendorAdapter" />
        <!-- Set base package of your entities -->
        <property name="packagesToScan">
            <array>
                <value>cz.wincor.ovv.batch.entity.batch</value>
            </array>
        </property>
        <property name="entityManagerFactoryInterface" value="jakarta.persistence.EntityManagerFactory"/>
    </bean>

        <!-- Configure the entity manager factory bean -->
    <bean id="ovvEntityManagerFactory"
        class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean">
        <property name="dataSource" ref="ovvDataSource" />
        <property name="jpaVendorAdapter" ref="hibernateJpaVendorAdapter" />
        <!-- Set base package of your entities -->
        <property name="packagesToScan">
            <array>
                <value>cz.wincor.ovv.batch.entity.ovv</value>
            </array>
        </property>
        <property name="entityManagerFactoryInterface" value="jakarta.persistence.EntityManagerFactory"/>
    </bean>

    <bean id="batchDataSourceParent" class="oracle.ucp.jdbc.PoolDataSourceImpl" abstract="true">
        <property name="connectionFactoryClassName" value="oracle.jdbc.pool.OracleDataSource"/>
        <property name="URL" value="${batch.jdbc.url}" />
        <property name="user" value="${batch.jdbc.user}" />
        <property name="password" value="${batch.jdbc.password}" />
        <property name="validateConnectionOnBorrow" value="${batch.jdbc.validateConnectionOnBorrow:true}" />
        <property name="fastConnectionFailoverEnabled" value="${batch.jdbc.fastConnectionFailoverEnabled:false}" />
        <property name="oNSConfiguration" value="${batch.jdbc.onsConfiguration:}" />
        <property name="maxPoolSize" value="${batch.jdbc.maxActive:50}" />
        <property name="minPoolSize" value="${batch.jdbc.minIdle:0}" />
        <property name="initialPoolSize" value="${batch.jdbc.initialSize:0}" />
        <property name="connectionWaitTimeout" value="${batch.jdbc.maxWait:3}" />
    </bean>

    <bean id="batchDataSource" parent="batchDataSourceParent" />
    <bean id="dataSource" parent="batchDataSource" />

    <!-- Enable annotation driven transaction management -->
    <tx:annotation-driven />

    <bean id="ovvDataSourceParent" class="oracle.ucp.jdbc.PoolDataSourceImpl" abstract="true">
        <property name="connectionFactoryClassName" value="oracle.jdbc.pool.OracleDataSource"/>
        <property name="URL" value="${ovv.jdbc.url}" />
        <property name="user" value="${ovv.jdbc.user}" />
        <property name="password" value="${ovv.jdbc.password}" />
        <property name="validateConnectionOnBorrow" value="${ovv.jdbc.validateConnectionOnBorrow:true}" />
        <property name="fastConnectionFailoverEnabled" value="${ovv.jdbc.fastConnectionFailoverEnabled:false}" />
        <property name="oNSConfiguration" value="${ovv.jdbc.onsConfiguration:}" />
        <property name="maxPoolSize" value="${ovv.jdbc.maxActive:25}" />
        <property name="minPoolSize" value="${ovv.jdbc.minIdle:0}" />
        <property name="initialPoolSize" value="${ovv.jdbc.initialSize:0}" />
        <property name="connectionWaitTimeout" value="${ovv.jdbc.maxWait:3}" />
    </bean>

    <bean id="ovvDataSource" parent="ovvDataSourceParent" />

    <jpa:repositories base-package="cz.wincor.ovv.batch.repository.batch" entity-manager-factory-ref="batchEntityManagerFactory" transaction-manager-ref="batchTransactionManager"  />
    <jpa:repositories base-package="cz.wincor.ovv.batch.repository.ovv" entity-manager-factory-ref="ovvEntityManagerFactory" transaction-manager-ref="ovvTransactionManager"  />

    <!-- Configure the transaction manager bean -->
    <bean id="batchTransactionManager" class="org.springframework.orm.jpa.JpaTransactionManager">
        <property name="entityManagerFactory" ref="batchEntityManagerFactory" />
    </bean>

    <!-- Configure the transaction manager bean -->
    <bean id="ovvTransactionManager" class="org.springframework.orm.jpa.JpaTransactionManager">
        <property name="entityManagerFactory" ref="ovvEntityManagerFactory" />
    </bean>

    <bean id="jobRepository"
        class="org.springframework.batch.core.repository.support.JobRepositoryFactoryBean"
        p:dataSource-ref="batchDataSource" p:transactionManager-ref="batchTransactionManager"
        p:isolationLevelForCreate="ISOLATION_READ_COMMITTED" p:lobHandler-ref="lobHandler"
        p:serializer-ref="executionContextStringSerializer" />

    <bean id="lobHandler" class="org.springframework.jdbc.support.lob.DefaultLobHandler">
        <!-- <property name="nativeJdbcExtractor" ref="nativeJdbcExtractor" /> -->
    </bean>

<!--    <bean id="nativeJdbcExtractor"-->
<!--        class="org.springframework.jdbc.support.nativejdbc.CommonsDbcpNativeJdbcExtractor" />-->

</beans>
