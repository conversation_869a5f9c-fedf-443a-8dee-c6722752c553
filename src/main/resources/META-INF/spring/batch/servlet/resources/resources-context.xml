<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:util="http://www.springframework.org/schema/util"
	xmlns:mvc="http://www.springframework.org/schema/mvc"
	xsi:schemaLocation="http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

	<context:annotation-config />

	<mvc:resources location="/,classpath:/META-INF/" mapping="resources/**" />

	<mvc:annotation-driven>
		<mvc:argument-resolvers>
			<ref bean="sortResolver"/>
			<ref bean="pageableResolver" />
			<ref bean="pagedResourcesAssemblerArgumentResolver" />
		</mvc:argument-resolvers>
	</mvc:annotation-driven>

	<bean id="sortResolver" class="org.springframework.data.web.HateoasSortHandlerMethodArgumentResolver" />

	<bean id="pageableResolver" class="org.springframework.data.web.HateoasPageableHandlerMethodArgumentResolver">
		<constructor-arg ref="sortResolver" />
	</bean>

	<bean id="pagedResourcesAssemblerArgumentResolver" class="org.springframework.data.web.PagedResourcesAssemblerArgumentResolver">
		<constructor-arg ref="pageableResolver" />
	</bean>

	<!-- Support for Spring Integration HTTP endpoints -->
	<bean class="org.springframework.web.servlet.mvc.SimpleControllerHandlerAdapter" />

	<!-- Support for Spring Integration HTTP endpoints -->
	<bean class="org.springframework.web.servlet.handler.BeanNameUrlHandlerMapping" />

	<!-- Support for Freemarker beans as views -->
	<bean class="org.springframework.web.servlet.view.BeanNameViewResolver" />

	<bean id="baseMenu" abstract="true">
		<property name="prefix" value="#{resourceService.servletPath}" />
	</bean>

	<bean id="menuManager" class="org.springframework.batch.admin.web.resources.MenuManager" />

	<bean class="org.springframework.batch.admin.web.util.HomeController" />

	<util:properties id="defaultResources" />
	<util:properties id="jsonResources" />

	<!-- freemarker config -->
	<bean id="freemarkerConfig" class="org.springframework.batch.admin.web.freemarker.HippyFreeMarkerConfigurer">
		<property name="templateLoaderPaths" value="/WEB-INF/web,classpath:/org/springframework/batch/admin/web" />
		<!-- TODO: find a way to switch this on for some template paths and not others -->
		<property name="preferFileSystemAccess" value="false" />
		<property name="freemarkerVariables">
			<map>
				<entry key="menuManager" value-ref="menuManager" />
			</map>
		</property>
		<property name="freemarkerSettings">
			<props>
				<prop key="default_encoding">UTF-8</prop>
				<prop key="output_encoding">UTF-8</prop>
			</props>
		</property>
	</bean>

	<bean id="parentLayout" class="org.springframework.batch.admin.web.freemarker.TagLibsFreeMarkerView" abstract="true">
		<property name="viewResolver">
			<bean class="org.springframework.web.servlet.view.freemarker.FreeMarkerViewResolver">
				<property name="exposeSpringMacroHelpers" value="false" />
				<property name="allowRequestOverride" value="true" />
			</bean>
		</property>
		<property name="attributes">
			<props merge="true">
				<prop key="titleCode">home.title</prop>
				<prop key="titleText">Spring Batch Admin</prop>
			</props>
		</property>
	</bean>

	<bean id="standard" parent="parentLayout">
		<property name="url" value="/layouts/html/standard.ftl" />
		<property name="contentType" value="text/html;charset=UTF-8" />
		<property name="attributes">
			<props merge="true">
				<!-- Matches the prefix of the servlet mapping in web.xml -->
				<prop key="servletPath">#{resourceService.servletPath}</prop>
			</props>
		</property>
	</bean>

	<bean id="standard.json" parent="parentLayout">
		<property name="url" value="/layouts/json/standard.ftl" />
		<property name="attributes">
			<props merge="true">
				<!-- Matches the prefix of the servlet mapping in web.xml -->
				<prop key="servletPath">#{resourceService.servletPath}</prop>
			</props>
		</property>
		<property name="contentType" value="application/json" />
	</bean>

</beans>
