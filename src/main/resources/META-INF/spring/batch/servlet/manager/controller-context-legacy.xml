<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:util="http://www.springframework.org/schema/util"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

	<!--<context:annotation-config />-->

	<bean class="org.springframework.batch.admin.web.JobController" />
	<bean class="org.springframework.batch.admin.web.StepExecutionController" />
	<bean class="org.springframework.batch.admin.web.JobExecutionController" />

	<bean id="objectMapper" class="com.fasterxml.jackson.databind.ObjectMapper"/>
</beans>
