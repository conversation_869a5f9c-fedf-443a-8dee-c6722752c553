<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:p="http://www.springframework.org/schema/p" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:context="http://www.springframework.org/schema/context"
    xmlns:batch="http://www.springframework.org/schema/batch" xmlns:int="http://www.springframework.org/schema/integration"
    xmlns:task="http://www.springframework.org/schema/task" xmlns:int-sftp="http://www.springframework.org/schema/integration/sftp"
    xmlns:int-file="http://www.springframework.org/schema/integration/file"
    xmlns:batch-integration="http://www.springframework.org/schema/batch-integration"
    xsi:schemaLocation="http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch.xsd
        http://www.springframework.org/schema/integration/sftp http://www.springframework.org/schema/integration/sftp/spring-integration-sftp.xsd
        http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd
        http://www.springframework.org/schema/batch-integration http://www.springframework.org/schema/batch-integration/spring-batch-integration.xsd
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/integration/file http://www.springframework.org/schema/integration/file/spring-integration-file.xsd
        http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd">

    <!-- Definition of the RECO job -->
    <batch:job id="plWasaRecoJob" incrementer="incrementer"
        restartable="true">
        <batch:step id="pre_step1-extractInputFile" parent="recoFileExtractStep"
            allow-start-if-complete="true" next="step1-findRelatedBatch" />
        <batch:step id="step1-findRelatedBatch" parent="findRelatedBatchFileStep"
            allow-start-if-complete="true" next="step2-reconciliate" />
        <batch:step id="step2-reconciliate" parent="readWriteRecoStep"
            next="step3-uploadResultToSftp" />
        <batch:step id="step3-uploadResultToSftp" parent="uploadToSftpStep"
            next="step4-archiveInputFileOnSftp" />
        <batch:step id="step4-archiveInputFileOnSftp" parent="archiveOnSftpStep"
            next="step5-archiveInputFileOnLocal" />
        <batch:step id="step5-archiveInputFileOnLocal" parent="fileArchiveStep" />
        <batch:listeners>
            <batch:listener ref="loggingMdcListener" />
            <batch:listener ref="jobListener" />
        </batch:listeners>
        <batch:validator ref="ovvRecoParameterValidator" />
    </batch:job>

    <!-- Step is responsible for uploading error files to a remote SFTP server -->
    <batch:step id="uploadToSftpStep">
        <batch:tasklet ref="sftpUploadTasklet" />
    </batch:step>

    <!-- Step is responsible for archiving files on a remote sftp server -->
    <batch:step id="archiveOnSftpStep">
        <batch:tasklet ref="sftpArchiveTasklet" />
    </batch:step>

    <!-- Tasklet responsible for uploading errors file to a remote SFTP server
        (if any file exists) -->
    <bean id="sftpUploadTasklet" class="cz.wincor.ovv.batch.tasklet.SftpUploadTasklet">
        <property name="uploadGateway" ref="sftpUploadGateway" />
        <property name="enabled" value="${batchJob.plWasa.sftp.upload.enabled:true}" />
    </bean>

    <!-- Tasklet responsible for archiving of a remote file on a remote SFTP
        server -->
    <bean id="sftpArchiveTasklet" class="cz.wincor.ovv.batch.tasklet.SftpArchiveTasklet">
        <property name="archiveGateway" ref="sftpArchiveGateway" />
        <property name="enabled" value="${batchJob.plWasa.sftp.upload.enabled:true}" />
    </bean>

    <!-- Channel section (used by spring integration) -->
    <!-- Downloaded file from a remote SFTP server is send to this channel,
        see a chain subscriber of this channel for more info about processing of
        downloaded files -->
    <int:channel id="downloadChannel" />
    <!-- Channel ready to accept archiving requests -->
    <int:channel id="archiveChannel" />
    <!-- Response channel from the archiving request -->
    <int:channel id="archiveChannelOutput" />
    <!-- Channel ready to accept files to be uploaded to a remote SFTP server -->
    <int:channel id="uploadErrorChannel" />
    <!-- Definition of a control channel, used by a control bus ready to accept
        management messages -->
    <int:channel id="controlChannel" />
    <!-- Control channel is responsible for manipulating with adapters, e.g.
        start a defined adapter -->
    <int:control-bus input-channel="controlChannel" />

    <!-- This adapter tries to download reconciliation file repeatedly -->
    <int-sftp:inbound-channel-adapter id="sftpInbondAdapter"
        channel="downloadChannel"
        filename-regex="${batchJob.plWasa.reconciliate.filename.matcher:(.*\.result$)|(.*\.result.zip$)}"
        session-factory="sftpSessionFactory" local-directory="${batch.job.base.path}/${batchJob.plWasa.reconciliate.input.path}"
        remote-directory="${batchJob.plWasa.sftp.remoteDirectory.download:/}"
        local-filter="localAcceptFilter" auto-startup="false"
        delete-remote-files="false">
        <int:poller max-messages-per-poll="2"
            cron="${batchJob.plWasa.reconciliate.cron:* */30 0-6 * * *}">
        </int:poller>
    </int-sftp:inbound-channel-adapter>

    <!-- This channel is responsible for uploading result files to a remote
        SFTP server (e.g. error files) -->
    <int-sftp:outbound-channel-adapter
        id="sftpOutboundAdapter" session-factory="sftpSessionFactory"
        channel="uploadErrorChannel" charset="UTF-8"
        remote-directory="${batchJob.plWasa.sftp.remoteDirectory.download:/}"
        auto-create-directory="true" />

    <!-- Chain which receives a downloaded reconciliated file and it runs a
        reconciliation job -->
    <int:chain input-channel="archiveChannel" output-channel="archiveChannelOutput">
        <int:header-enricher>
            <int:header name="file_renameTo"
                expression="'${batchJob.plWasa.sftp.remoteDirectory.archive:/archive}' + '/' + payload" />
        </int:header-enricher>
        <int-sftp:outbound-gateway command="mv"
            session-factory="sftpSessionFactory"
            expression="'${batchJob.plWasa.sftp.remoteDirectory.download:/}'+ '/' + payload" />
    </int:chain>

    <!-- In memory filter to define number of tries to accept a local file -->
    <bean id="localAcceptFilter" class="cz.wincor.ovv.batch.support.RepeatedFileListFilter">
        <constructor-arg value="${batchJob.plWasa.reconciliate.maxRepeatCount:1}" />
        <constructor-arg
            value="${batchJob.plWasa.reconciliate.filename.matcher:(.*\.result$)|(.*\.result.zip$)}" />
    </bean>

    <!-- Chain which receives a downloaded reconciliated file and it runs a
        reconciliation job -->
    <int:chain input-channel="downloadChannel">
        <int:transformer>
            <bean class="cz.wincor.ovv.batch.support.FileMessageToJobRequest">
                <property name="job" ref="plWasaRecoJob" />
                <property name="jobParameters">
                    <bean class="org.springframework.batch.core.JobParameters">
                        <constructor-arg>
                            <map>
                                <entry key="input.file.columns">
                                    <bean class="org.springframework.batch.core.JobParameter">
                                        <constructor-arg index="0" value="${batchJob.plWasa.reconciliate.input.file.columns:voucherNumber,resultCode,resultDescription}"/>
                                        <constructor-arg index="1" type="java.lang.Class" value="java.lang.String"/>
                                    </bean>
                                </entry>
                                <entry key="input.file.delimiter">
                                    <bean class="org.springframework.batch.core.JobParameter">
                                        <constructor-arg index="0" value="${batchJob.plWasa.reconciliate.input.file.delimiter:;}"/>
                                        <constructor-arg index="1" type="java.lang.Class" value="java.lang.String"/>
                                    </bean>
                                </entry>
                                <entry key="input.file.linesToSkip">
                                    <bean class="org.springframework.batch.core.JobParameter">
                                        <constructor-arg index="0" value="${batchJob.plWasa.reconciliate.input.file.linesToSkip:1}"/>
                                        <constructor-arg index="1" type="java.lang.Class" value="java.lang.Long"/>
                                    </bean>
                                </entry>
                                <entry key="failOnMissingVoucher">
                                    <bean class="org.springframework.batch.core.JobParameter">
                                        <constructor-arg index="0" value="${batchJob.plWasa.reconciliate.failOnMissingVoucher:false}"/>
                                        <constructor-arg index="1" type="java.lang.Class" value="java.lang.String"/>
                                    </bean>
                                </entry>
                                <entry key="output.path">
                                    <bean class="org.springframework.batch.core.JobParameter">
                                        <constructor-arg index="0" value="${batch.job.base.path}/${batchJob.plWasa.reconciliate.output.path}"/>
                                        <constructor-arg index="1" type="java.lang.Class" value="java.lang.String"/>
                                    </bean>
                                </entry>
                            </map>
                        </constructor-arg>
                    </bean>
                </property>
            </bean>
        </int:transformer>
        <batch-integration:job-launching-gateway />
        <int:logging-channel-adapter />
    </int:chain>

    <!-- This bean is responsible for starting sftpInbondAdapter if required
        to download periodically remote reconciliation files -->
    <bean class="cz.wincor.ovv.batch.support.AdapterControlBusStarter"
        depends-on="sftpInbondAdapter">
        <constructor-arg value="${batchJob.plWasa.reconciliate.enabled:false}" />
        <constructor-arg value="sftpInbondAdapter" />
        <constructor-arg ref="controlChannel" />
    </bean>

    <bean id="sftpSessionFactoryCached"
        class="org.springframework.integration.file.remote.session.CachingSessionFactory">
        <constructor-arg ref="sftpSessionFactory" />
    </bean>

    <!-- SFTP session factory used by all interactions with a remote sftp server -->
    <bean id="sftpSessionFactory"
        class="org.springframework.integration.sftp.session.DefaultSftpSessionFactory">
        <constructor-arg value="true" />
        <property name="timeout" value="30000" />
        <property name="host" value="${batchJob.plWasa.sftp.host}" />
        <property name="password" value="${batchJob.plWasa.sftp.password}" />
        <property name="port" value="${batchJob.plWasa.sftp.serverPort}" />
        <property name="user" value="${batchJob.plWasa.sftp.username}" />
        <property name="privateKey"
            value="${batchJob.plWasa.sftp.private.keyfile:#{null}}" />
        <property name="privateKeyPassphrase" value="${batchJob.plWasa.sftp.passphrase}" />
        <property name="allowUnknownKeys" value="true" />
    </bean>
    <!-- the main gateway which uploads a file to a remote sftp -->
    <int:gateway id="sftpUploadGateway"
        service-interface="cz.wincor.ovv.batch.gateway.UploadToSftpGateway"
        default-request-channel="uploadErrorChannel" default-reply-timeout="10000" />

    <!-- the main gateway which archive a file on a remote sftp -->
    <int:gateway id="sftpArchiveGateway"
        service-interface="cz.wincor.ovv.batch.gateway.ArchiveSftpGateway"
        default-request-channel="archiveChannel" default-reply-channel="archiveChannelOutput"
        default-reply-timeout="10000" />

</beans>
