<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:p="http://www.springframework.org/schema/p" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:context="http://www.springframework.org/schema/context"
    xmlns:batch="http://www.springframework.org/schema/batch" xmlns:int="http://www.springframework.org/schema/integration"
    xmlns:task="http://www.springframework.org/schema/task" xmlns:int-sftp="http://www.springframework.org/schema/integration/sftp"
    xmlns:int-file="http://www.springframework.org/schema/integration/file"
    xsi:schemaLocation="http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch.xsd
        http://www.springframework.org/schema/integration/sftp http://www.springframework.org/schema/integration/sftp/spring-integration-sftp.xsd
        http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/integration/file http://www.springframework.org/schema/integration/file/spring-integration-file.xsd
        http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd">


    <!-- Definition of the Batch job -->
    <batch:job id="skEdenredGiftBatchJob" restartable="true"
        incrementer="incrementer">
        <batch:step id="pre_step1-setupOutputFile" parent="setupOutputFileStep"
            next="pre_step2-setupBatchInterval" allow-start-if-complete="true" />
        <batch:step id="pre_step2-setupBatchInterval" parent="setupBatchIntervalStep"
            next="pre_step3-loadOvvStores" allow-start-if-complete="true" />
        <batch:step id="pre_step3-loadOvvStores" parent="loadOvvStoresStep"
            next="step1-readWrite" allow-start-if-complete="true" />
        <batch:step id="step1-readWrite" parent="readWriteStep"
            next="step2-addItemNumber" />
        <batch:step id="step2-addItemNumber" parent="addItemNumberStep"
            next="step3-compressBatchFile" />
        <batch:step id="step3-compressBatchFile" parent="compressBatchFileStep"
            allow-start-if-complete="true" next="step4-uploadToSftp" />
        <batch:step id="step4-uploadToSftp" parent="uploadToSftpStep"
            allow-start-if-complete="true" />
        <batch:listeners>
            <batch:listener ref="loggingMdcListener" />
            <batch:listener ref="jobListener" />
        </batch:listeners>
        <batch:validator ref="ovvBatchParameterValidator" />
    </batch:job>

    <!-- This step is responsible for uploading batch files to a remote SFTP -->
    <batch:step id="uploadToSftpStep">
        <batch:tasklet ref="sftpUploadTasklet" />
    </batch:step>

    <!-- Tasklet responsible for uploading batch file to a remote SFTP server -->
    <bean id="sftpUploadTasklet" class="cz.wincor.ovv.batch.tasklet.SftpUploadTasklet">
        <property name="uploadGateway" ref="sftpUploadGateway" />
        <property name="enabled"
            value="${batchJob.skEdenred.sftp.upload.enabled:true}" />
    </bean>

    <bean id="batchFileCompressTasklet" class="cz.wincor.ovv.batch.tasklet.BatchFileCompressTasklet">
        <property name="compressingEnabled"
            value="${batchJob.skEdenred.sftp.compressFile.enabled:false}" />
    </bean>

    <!-- Definition of a JDBC item reader, i.e. reader which reads data from
        transaction log -->
    <bean id="itemReader"
        class="org.springframework.batch.item.database.JdbcPagingItemReader"
        scope="step">
        <property name="dataSource" ref="ovvDataSource" />
        <property name="queryProvider">
            <bean
                class="org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean">
                <property name="dataSource" ref="ovvDataSource" />
                <property name="selectClause"
                    value="select req.id as requestId, v.id as voucherId, req.device_id as deviceId, req.device_local_date_time as deviceLocalDateTime, req.server_utc_date_time as serverUtcDateTime, v.server_local_date_time as serverLocalDateTime, req.amount as amount, req.pos_currency_code as currencyCode, req.partner_id as partnerId, req.payment_place as paymentPlace, req.stan as stan, req.transaction_type as transactionType, v.voucher_number as voucherNumber, req.country_code as countryCode" />
                <property name="fromClause"
                    value="from VOUCHER v join OVV_TRANSACTION_REQUEST req on (v.related_transaction_request_id = req.id)" />
                <property name="whereClause"
                    value="where req.country_code = :countryCode and req.partner_id in (:partnerId) and v.voucher_state = 'USED' and req.ovv_issuer = :ovvIssuer and v.server_local_date_time between :startDate and :endDate and v.voucher_number like :voucherPrefix" />
                <property name="sortKey" value="voucherId" />
            </bean>
        </property>
        <property name="parameterValues">
            <map>
                <entry key="startDate" value="#{jobExecutionContext['reader.startDateTime']}" />
                <entry key="endDate" value="#{jobExecutionContext['reader.endDateTime']}" />
                <entry key="ovvIssuer" value="#{jobParameters['ovvIssuer']}" />
                <entry key="partnerId" value="#{jobExecutionContext['partnerId']}" />
                <entry key="countryCode" value="#{jobParameters['output.file.name.country']}" />
                <entry key="voucherPrefix" value="#{jobParameters['voucher.prefix']}%" />
            </map>
        </property>
        <property name="pageSize" value="#{jobParameters['pageSize']}" />
        <property name="rowMapper">
            <bean class="cz.wincor.ovv.batch.support.VoucherRowMapper">
                <property name="countryCode"
                    value="#{jobParameters['output.file.name.country']}" />
                <property name="receiverTimezone" value="#{jobParameters['receiverTimezone']}" />
                <property name="storeManager" value="#{jobExecutionContext['ovvStoreManager']}"/>
            </bean>
        </property>
    </bean>

    <bean id="batchFileHeaderTasklet" scope="job"
        class="cz.wincor.ovv.batch.tasklet.BatchFileHeaderTasklet">
        <property name="additionalHeaderValues">
            <list>
                <value>#{jobParameters['voucher.prefix']}</value>
            </list>
        </property>
    </bean>

    <!-- Scheduler definition, i.e. what will be executed when a batch generation
        scheduler will be fired -->
    <bean id="scheduler" class="cz.wincor.ovv.batch.scheduler.WeeklyJobScheduler">
        <property name="launcher" ref="jobLauncher" />
        <property name="job" ref="skEdenredGiftBatchJob" />
        <property name="enabled" value="${batchJob.skEdenred.generate.enabled:false}" />
        <property name="jobParameters">
            <bean class="org.springframework.batch.core.JobParameters">
                <constructor-arg>
                    <map>
                        <entry key="pageSize">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batchJob.skEdenred.generate.pageSize:1000}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                        <!-- Possible columns are: voucherNumber, storeNumber, costCentre -->
                        <entry key="output.file.columns">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batchJob.skEdenred.generate.output.file.columns:voucherNumber,storeNumber}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                        <entry key="output.file.delimiter">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batchJob.skEdenred.generate.output.file.delimiter:;}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                        <entry key="#{T(cz.wincor.ovv.batch.BatchConstants).PARAM_OUTPUT_PATH}">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batch.job.base.path}/${batchJob.skEdenred.generate.output.file.path}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                        <entry key="#{T(cz.wincor.ovv.batch.BatchConstants).PARAM_OUTPUT_FILENAME_COUNTRY}">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batchJob.skEdenred.generate.output.file.name.country}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                        <entry key="#{T(cz.wincor.ovv.batch.BatchConstants).PARAM_OVV_ISSUER}">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batchJob.skEdenred.generate.ovvIssuer}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                        <entry key="#{T(cz.wincor.ovv.batch.BatchConstants).PARAM_PARTNER_ID}">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batchJob.skEdenred.generate.partnerId:31321828}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                        <entry key="#{T(cz.wincor.ovv.batch.BatchConstants).PARAM_RECEIVER_TIMEZONE}">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batchJob.skEdenred.generate.receiverTimezone:Europe/Prague}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                        <entry key="voucher.prefix">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batchJob.skEdenred.generate.gift.voucher.prefix}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                    </map>
                </constructor-arg>
            </bean>
        </property>
    </bean>

    <task:scheduled-tasks>
        <task:scheduled ref="scheduler" method="run"
            cron="${batchJob.skEdenred.generate.gift.cron:0 10 1 * * *}" />
    </task:scheduled-tasks>

    <!-- Gateway interface for tuploadChannel channel -->
    <int:gateway id="sftpUploadGateway"
        service-interface="cz.wincor.ovv.batch.gateway.UploadToSftpGateway"
        default-request-channel="uploadChannel" default-reply-timeout="10000" />

    <bean id="sftpSessionFactory"
        class="org.springframework.integration.sftp.session.DefaultSftpSessionFactory">
        <property name="host" value="${batchJob.skEdenred.sftp.host}" />
        <property name="password" value="${batchJob.skEdenred.sftp.password}" />
        <property name="port" value="${batchJob.skEdenred.sftp.serverPort}" />
        <property name="user" value="${batchJob.skEdenred.sftp.username}" />
        <property name="privateKey"
            value="${batchJob.skEdenred.sftp.private.keyfile:#{null}}" />
        <property name="privateKeyPassphrase" value="${batchJob.skEdenred.sftp.passphrase}" />
        <property name="allowUnknownKeys" value="true" />
    </bean>

    <!-- Outbound channel adapter uploads files to a remote SFTP server -->
    <int-sftp:outbound-channel-adapter
        id="sftpOutboundAdapter" session-factory="sftpSessionFactory" channel="uploadChannel"
        charset="UTF-8" remote-directory="${batchJob.skEdenred.sftp.remoteDirectory.upload:/}"
        auto-create-directory="true" />

    <int:channel id="uploadChannel" />




</beans>
