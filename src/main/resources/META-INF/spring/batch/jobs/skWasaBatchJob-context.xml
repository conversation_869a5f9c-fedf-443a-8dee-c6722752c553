<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:p="http://www.springframework.org/schema/p" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:context="http://www.springframework.org/schema/context"
    xmlns:batch="http://www.springframework.org/schema/batch" xmlns:int="http://www.springframework.org/schema/integration"
    xmlns:task="http://www.springframework.org/schema/task" xmlns:int-sftp="http://www.springframework.org/schema/integration/sftp"
    xmlns:int-file="http://www.springframework.org/schema/integration/file"
    xsi:schemaLocation="http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch.xsd
        http://www.springframework.org/schema/integration/sftp http://www.springframework.org/schema/integration/sftp/spring-integration-sftp.xsd
        http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/integration/file http://www.springframework.org/schema/integration/file/spring-integration-file.xsd
        http://www.springframework.org/schema/integration http://www.springframework.org/schema/integration/spring-integration.xsd">


    <!-- Definition of the Batch job -->
    <batch:job id="skWasaBatchJob" restartable="true"
        incrementer="incrementer">
        <batch:step id="pre_step1-setupOutputFile" parent="setupOutputFileStep"
            next="pre_step2-setupBatchInterval" allow-start-if-complete="true" />
         <batch:step id="pre_step2-setupBatchInterval" parent="setupBatchIntervalStep"
            next="pre_step3-loadOvvStores" allow-start-if-complete="true" />
        <batch:step id="pre_step3-loadOvvStores" parent="loadOvvStoresStep"
            next="step1-readWrite" allow-start-if-complete="true" />
        <batch:step id="step1-readWrite" parent="readWriteStep"
            next="step2-addItemNumber" />
        <batch:step id="step2-addItemNumber" parent="addItemNumberStep"
            next="step3-compressBatchFile" />
        <batch:step id="step3-compressBatchFile" parent="compressBatchFileStep"
            allow-start-if-complete="true" next="step4-uploadToSftp" />
        <batch:step id="step4-uploadToSftp" parent="uploadToSftpStep"
            allow-start-if-complete="true" />
        <batch:listeners>
            <batch:listener ref="loggingMdcListener" />
            <batch:listener ref="jobListener" />
        </batch:listeners>
        <batch:validator ref="ovvBatchParameterValidator" />
    </batch:job>

    <!-- This step is responsible for uploading batch files to a remote SFTP -->
    <batch:step id="uploadToSftpStep">
        <batch:tasklet ref="sftpUploadTasklet" />
    </batch:step>

    <!-- Tasklet responsible for uploading batch file to a remote SFTP server -->
    <bean id="sftpUploadTasklet" class="cz.wincor.ovv.batch.tasklet.SftpUploadTasklet">
        <property name="uploadGateway" ref="sftpUploadGateway" />
        <property name="enabled" value="${batchJob.skWasa.sftp.upload.enabled:true}" />
    </bean>

    <bean id="batchFileCompressTasklet" class="cz.wincor.ovv.batch.tasklet.BatchFileCompressTasklet">
        <property name="compressingEnabled" value="${batchJob.skWasa.sftp.compressFile.enabled:false}"/>
    </bean>

    <!-- Scheduler definition, i.e. what will be executed when a batch generation
        scheduler will be fired -->
    <bean id="scheduler" class="cz.wincor.ovv.batch.scheduler.DailyJobScheduler">
        <property name="launcher" ref="jobLauncher" />
        <property name="job" ref="skWasaBatchJob" />
        <property name="enabled" value="${batchJob.skWasa.generate.enabled:false}" />
        <property name="jobParameters">
            <bean class="org.springframework.batch.core.JobParameters">
                <constructor-arg>
                    <map>
                        <entry key="pageSize">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batchJob.skWasa.generate.pageSize:1000}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                        <!-- Possible columns are: voucherNumber, storeNumber, costCentre -->
                        <entry key="output.file.columns">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batchJob.skWasa.generate.output.file.columns:voucherNumber,storeNumber}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                        <entry key="output.file.delimiter">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batchJob.skWasa.generate.output.file.delimiter:;}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                        <entry key="#{T(cz.wincor.ovv.batch.BatchConstants).PARAM_OUTPUT_PATH}">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batch.job.base.path}/${batchJob.skWasa.generate.output.file.path}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                        <entry key="#{T(cz.wincor.ovv.batch.BatchConstants).PARAM_OUTPUT_FILENAME_COUNTRY}">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batchJob.skWasa.generate.output.file.name.country}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                        <entry key="#{T(cz.wincor.ovv.batch.BatchConstants).PARAM_OVV_ISSUER}">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batchJob.skWasa.generate.ovvIssuer}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                        <entry key="#{T(cz.wincor.ovv.batch.BatchConstants).PARAM_PARTNER_ID}">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batchJob.skWasa.generate.partnerId:31321828}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                        <entry key="#{T(cz.wincor.ovv.batch.BatchConstants).PARAM_RECEIVER_TIMEZONE}">
                            <bean class="org.springframework.batch.core.JobParameter">
                                <constructor-arg index="0" value="${batchJob.skWasa.generate.receiverTimezone:Europe/Prague}" />
                                <constructor-arg index="1" type="java.lang.Class" value="java.lang.String" />
                            </bean>
                        </entry>
                    </map>
                </constructor-arg>
            </bean>
        </property>
    </bean>

    <task:scheduled-tasks>
        <task:scheduled ref="scheduler" method="run"
            cron="${batchJob.skWasa.generate.cron:0 10 1 * * *}" />
    </task:scheduled-tasks>

    <!-- Gateway interface for tuploadChannel channel -->
    <int:gateway id="sftpUploadGateway"
        service-interface="cz.wincor.ovv.batch.gateway.UploadToSftpGateway"
        default-request-channel="uploadChannel" default-reply-timeout="10000" />

    <bean id="sftpSessionFactory"
        class="org.springframework.integration.sftp.session.DefaultSftpSessionFactory">
        <property name="host" value="${batchJob.skWasa.sftp.host}" />
        <property name="password" value="${batchJob.skWasa.sftp.password}" />
        <property name="port" value="${batchJob.skWasa.sftp.serverPort}" />
        <property name="user" value="${batchJob.skWasa.sftp.username}" />
        <property name="privateKey"
            value="${batchJob.skWasa.sftp.private.keyfile:#{null}}" />
        <property name="privateKeyPassphrase" value="${batchJob.skWasa.sftp.passphrase}" />
        <property name="allowUnknownKeys" value="true" />
    </bean>

    <!-- Outbound channel adapter uploads files to a remote SFTP server -->
    <int-sftp:outbound-channel-adapter
        id="sftpOutboundAdapter" session-factory="sftpSessionFactory"
        channel="uploadChannel" charset="UTF-8"
        remote-directory="${batchJob.skWasa.sftp.remoteDirectory.upload:/}"
        auto-create-directory="true" />

    <int:channel id="uploadChannel" />




</beans>
