<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <property name="LOG_PATH"
        value="${ovvbatch.logging.path:-${java.io.tmpdir:-/tmp}}" />

    <appender name="FILE-JOB" class="ch.qos.logback.classic.sift.SiftingAppender">
        <!-- This is MDC value -->
        <!-- We will assign a value to 'jobName' via Java code -->
        <discriminator>
            <key>jobName</key>
            <defaultValue>ovv-batch</defaultValue>
        </discriminator>
        <sift>
            <appender name="FILE-${jobName}"
                class="ch.qos.logback.core.rolling.RollingFileAppender">
                <encoder>
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %msg
                        [%logger:%line]%n</pattern>
                </encoder>
                <file>${LOG_PATH}/${jobName}.log</file>
                <rollingPolicy
                    class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>${LOG_PATH}/${jobName}.%i.log.zip
                    </fileNamePattern>
                </rollingPolicy>
                <triggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <MaxFileSize>10MB</MaxFileSize>
                </triggeringPolicy>
            </appender>
        </sift>
    </appender>

    <appender name="FILE"
        class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %msg
                        [%logger:%line]%n</pattern>
        </encoder>
        <file>${LOG_PATH}/ovv-batch-full.log</file>
         <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- rollover daily -->
            <fileNamePattern>${LOG_PATH}/ovv-batch-full-%d{yyyy-MM-dd}_%i.log.zip
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedFileNamingAndTriggeringPolicy">
                <!-- or whenever the file size reaches 2MB -->
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <logger name="org.springframework.integration.sftp.session.SftpSession"
        level="DEBUG" />
    <logger name="org.springframework.integration.endpoint.SourcePollingChannelAdapter"
        level="INFO" />
    <logger name="cz.wincor" level="TRACE" />
    <logger name="org.springframework" level="DEBUG" />
    <logger name="org.springframework.integration.sftp.inbound.SftpInboundFileSynchronizer"
            level="DEBUG" />
    <logger name="org.springframework.batch.admin.service.SimpleJobService"
            level="DEBUG" />
    <logger name="org.springframework.batch.core.job"
            level="DEBUG" />


    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %msg [%logger:%line]%n
            </pattern>
        </encoder>
    </appender>

    <root level="DEBUG">
        <appender-ref ref="FILE" />
    </root>
</configuration>
