#Concurrency limit to some heavy tasks
#Set the maximum number of parallel accesses allowed. -1 indicates no concurrency limit at all.
#Default value: current number of available processors
#batch.async.availableProcessors=1

#Configuration for xml files with job definitions, this is not path to *.properties files
#Currently it is not used to job configuration and property is set to ${java.io.tmpdir} (you can leave it as it)
batch.job.configuration.file.dir=${java.io.tmpdir}/ovv-batch-jobs

#Base path where all files will be generated and downloaded from SFTP server
#Used by job configurations, where only relative path is present
batch.job.base.path=${java.io.tmpdir}

######## RSA SecurID login - RADIUS ###########################################
batch.authentication.radius.enabled=false
batch.authentication.radius.server=127.0.0.1;secret;3;127.0.0.1;secret;10
batch.authentication.radius.radiusid=OVVBatch

######## Rescheduler of failing jobs #######
batch.job.failed.reschedule.enabled = true
#How many times should be failed job rescheduled
batch.job.failed.reschedule.maxRepeatCount = 4
#Definition of delays in hours for each repeat count (count of values must be equal to maxRepeatCount)
#Each of delay will be applied from the last failed job execution (i.e. not always from the first run)
batch.job.failed.reschedule.delays = 4;5;20;20

