##Batch (internal) database setting
batch.data.source.init=false
##Database setting
#Database vendor, possible values: DB2, DERBY, H2, HSQL, INFORMIX, MYSQL, ORACLE, POSTGRESQL, SQL_SERVER, SYBASE,
#required value
batch.database.vendor = OR<PERSON>LE
#Related Driver class name, required
batch.jdbc.driver=oracle.jdbc.OracleDriver
#Database URL, required
batch.jdbc.url=****************************************
#Database username, required
batch.jdbc.user=ovv
#Database password, required
batch.jdbc.password=ovv
#Whether real SQL commands should be logged
#Possible values:true, false
#Optional, default value: false
#batch.database.showSql=true
#validateConnectionOnBorrow, default true
#batch.jdbc.validateConnectionOnBorrow=true
#fastConnectionFailoverEnabled, Default false
#batch.jdbc.fastConnectionFailoverEnabled=true
#batch.jdbc.onsConfiguration=10.100.121.52:6200,10.100.121.53:6200

#The maximum number of active connections that can be allocated from this pool at the same time, or negative for no limit.
#Default value: 50
#batch.jdbc.maxActive=10

#The minimum number of connections that can remain idle in the pool, without extra ones being created, or zero to create none.
#Default value: 0
#batch.jdbc.minIdle=0

#The initial number of connections that are created when the pool is started.
#Default value: 0
#batch.jdbc.initialSize=0

#The maximum number of seconds that the pool will wait (when there are no available connections)
# for a connection to be returned before throwing an exception, or 0 to wait indefinitely.
#Default value: 3
#batch.jdbc.maxWait=3



##OVV Database setting (for reading from tansaction log)
ovv.jdbc.driver=oracle.jdbc.OracleDriver
ovv.jdbc.url=*******************************************
ovv.jdbc.user=ocvv
ovv.jdbc.password=ocvv
#ovv.jdbc.validateConnectionOnBorrow=true
#ovv.jdbc.fastConnectionFailoverEnabled=true
#ovv.jdbc.onsConfiguration=10.100.121.52:6200,10.100.121.53:6200
#ovv.jdbc.maxActive=25
#ovv.jdbc.minIdle=0
#ovv.jdbc.initialSize=0
#ovv.jdbc.maxWait=3
