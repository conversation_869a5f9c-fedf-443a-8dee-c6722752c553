###BatchJob setting
#SK DOXX Batch Generator
#Whether skDoxxBatchjob is enabled or not
#Possible values: true/false
#Default value (if configuration is missing is false)
batchJob.skDoxx.generate.enabled = true

#How often run batchJob for SK DOXX issuer
#Default value is 0 10 1 * * * if the following property is missing (every day at 01:10:00)
batchJob.skDoxx.generate.cron = 0 20 1 * * *

#Time zone of the reciever
#It will be used to select required vouchers between midnight in a defined timezone
#Example of SOME possible values: UTC, Europe/Moscow, Europe/London, Europe/Budapest, Europe/Bratislava, Europe/Vienna, Europe/Warsaw, Europe/Prague
#Default value is Europe/Prague if a property is missing
#batchJob.skDoxx.generate.receiverTimezone = Europe/Prague

#How many rows read process in one step (to avoid out of memory error)
#Default value is 1000 if the following property is missing
#batchJob.skDoxx.generate.pageSize = 1000

#Which columns will be in a report file
#Default value is: voucherNumber, storeNumber
#Possible values:id, voucherNumber, storeNumber, costCentre, serverDateTimeIsoFormat, posDateTimeIsoFormat (delimited by commas)
batchJob.skDoxx.generate.output.file.columns = voucherNumber, storeNumber, costCentre, posDateTimeIsoFormat

#Column delimiter in a report file
#Default value is ; (semicolon) if the following property is missing
#batchJob.skDoxx.generate.output.file.delimiter = ;

#Path where a report file will be created (relative, if not exist, will be created)
#Path is relative to batch.job.base.path
#Required
batchJob.skDoxx.generate.output.file.path = skDoxxJob/batch

#Name of country used in a report file name
#Required
batchJob.skDoxx.generate.output.file.name.country = SK

#Ovv Issuer related to this job instance,
#used in a report file name and as a where clause in the select into a transaction log
#Required
batchJob.skDoxx.generate.ovvIssuer = DOXX

##SK DOXX Reconciliation
#Whether reconciliation of the skDoxxBatchjob is enabled or not
#Possible values: true/false
#Default value (if configuration is missing) is false
batchJob.skDoxx.reconciliate.enabled = false

#How often run batchJob reconciliation for SK DOXX issuer
#Default value is * */30 0-6 * * * if the following property is missing (every half hour from 00:00:00 to 06:59:59)
batchJob.skDoxx.reconciliate.cron = 0 5/10 * * * *

#Which columns will be in a report file
#Default value is: voucherNumber,resultCode,resultDescription
#Possible values:id, voucherNumber,resultCode,resultDescription (delimited by commas)
#batchJob.skDoxx.reconciliate.input.file.columns = voucherNumber,resultCode,resultDescription

#Column delimiter in a report file
#Default value is ; (semicolon) if the following property is missing
#batchJob.skDoxx.reconciliate.input.file.delimiter = ;

#How many columns should be skipped before reading an input file
#Default value is 1 if the following property is missing
#batchJob.skDoxx.reconciliate.input.file.linesToSkip = 1

#Path where an issuer's response file should be downloaded to (relative, if not exist, will be created)
#Path is relative to batch.job.base.path
#Required
batchJob.skDoxx.reconciliate.input.path = skDoxxJob/reco/input

#Path where a processed (i.e. already reconciliated) files should be moved to (relative, if not exist, will be created)
#Path is relative to batch.job.base.path
#Required
batchJob.skDoxx.reconciliate.output.path = skDoxxJob/reco/output

#Max Repeat Count to reconciliate file (i.e. repeated calls will be applied in a case of an error in a processing flow only)
#Counter will be reset after an application restart
#Possible values: Integer.MAX_VALUE
#Default value (if configuration is missing) is 1 = i.e. accept only one
batchJob.skDoxx.reconciliate.maxRepeatCount = 5

#Whether a reconciliate job should fail in a case of receiving voucher, that is missing in the our DB
#If set to true, only exception is thrown
#If set to false (default value), error message will be added to a error result file
#Default value (if configuration is missing) is false
#batchJob.skDoxx.reconciliate.failOnMissingVoucher = true

#Regular expression which file names should be downloaded from a remote server and reconciliated
#Default value (if configuration is missing) is (.*\.result$)|(.*\.result.zip$)
#batchJob.skDoxx.reconciliate.filename.matcher=(.*\.result$)|(.*\.result.zip$)

##SK DOXX SFTP server setting, all properties are required
#Whether a result report file should be uploaded to sftp server as a part of a generation phase
#Possible values: true/false
#Default value (if configuration is missing is true)
batchJob.skDoxx.sftp.upload.enabled=false
#name, or the IP address, of the server you want to connect to
batchJob.skDoxx.sftp.host=127.0.0.1
#specify which port number on the server to connect to
batchJob.skDoxx.sftp.serverPort=22
#your account name on the server
batchJob.skDoxx.sftp.username=ovv
#password associated with the account.
#batchJob.skDoxx.sftp.password=test
#Specify a key associated with the account (instead of password)
#Prefix file:// is required in all cases, linux link '~/' to home folder cannot be used
#Expected format is a private key in openssh format, e.g. the same as ~/.ssh/id_rsa
#if you do not want to use key file, comment the following line
batchJob.skDoxx.sftp.private.keyfile=file:///home/<USER>/.ssh/id_rsa
#Specify a passphrase to a key associated with the account (instead of password)
batchJob.skDoxx.sftp.passphrase=ovvbatch
#Whether a file should be zipped before uploading to SFTP
#Default value = false (CSV file will be uploaded)
#True means an input CSV file will be zipped and upoaded to SFTP instead of CSV file
batchJob.skDoxx.sftp.compressFile.enabled = false
#Directory to upload local files (related to a batch generation phase)
#Default value is: /
batchJob.skDoxx.sftp.remoteDirectory.upload = sftp/skDoxx
#Remote directory to download files (related to a batch reconciliation phase)
#Default value is: /
batchJob.skDoxx.sftp.remoteDirectory.download = sftp/skDoxx
#Remote directory to archive files (after successfully downloading)
#Default value is: /archive
batchJob.skDoxx.sftp.remoteDirectory.archive = sftp/skDoxx/archive
