###BatchJob setting
#HU Edenred Batch Generator
#Whether huEdenredBatchjob is enabled or not
#Possible values: true/false
#Default value (if configuration is missing is false)
batchJob.huEdenred.generate.enabled = true

#How often run batchJob for HU Edenred issuer
#Default value is 0 10 1 * * * if the following property is missing (every day at 01:10:00)
batchJob.huEdenred.generate.cron = 0 50 0 * * *

#Time zone of the reciever
#It will be used to select required vouchers between midnight in a defined timezone
#Example of SOME possible values: UTC, Europe/Moscow, Europe/London, Europe/Budapest, Europe/Bratislava, Europe/Vienna, Europe/Warsaw, Europe/Prague
#Default value is Europe/Prague if a property is missing
#batchJob.huEdenred.generate.receiverTimezone = Europe/Prague

#How many rows read process in one step (to avoid out of memory error)
#Default value is 1000 if the following property is missing
#batchJob.huEdenred.generate.pageSize = 1000

#Which columns will be in a report file
#Default value is: voucherNumber, storeNumber
#Possible values:id, voucherNumber, storeNumber, costCentre, serverDateTimeIsoFormat, posDateTimeIsoFormat (delimited by commas)
batchJob.huEdenred.generate.output.file.columns = voucherNumber, storeNumber, costCentre

#Column delimiter in a report file
#Default value is ; (semicolon) if the following property is missing
#batchJob.huEdenred.generate.output.file.delimiter = ;

#Path where a report file will be created (relative, if not exist, will be created)
#Path is relative to batch.job.base.path
#Required
batchJob.huEdenred.generate.output.file.path = huEdenredJob/batch

#Name of country used in a report file name
#Required
batchJob.huEdenred.generate.output.file.name.country = HU

#Ovv Issuer related to this job instance,
#used in a report file name and as a where clause in the select into a transaction log
#Required
batchJob.huEdenred.generate.ovvIssuer = EDENRED

##HU Edenred Reconciliation
#Whether reconciliation of the huEdenredBatchjob is enabled or not
#Possible values: true/false
#Default value (if configuration is missing) is false
batchJob.huEdenred.reconciliate.enabled = false

#How often run batchJob reconciliation for HU Edenred issuer
#Default value is * */30 0-6 * * * if the following property is missing (every half hour from 00:00:00 to 06:59:59)
batchJob.huEdenred.reconciliate.cron = 0 2/10 * * * *

#Which columns will be in a report file
#Default value is: voucherNumber,resultCode,resultDescription
#Possible values:id, voucherNumber,resultCode,resultDescription (delimited by commas)
#batchJob.huEdenred.reconciliate.input.file.columns = voucherNumber,resultCode,resultDescription

#Column delimiter in a report file
#Default value is ; (semicolon) if the following property is missing
#batchJob.huEdenred.reconciliate.input.file.delimiter = ;

#How many columns should be czipped before reading an input file
#Default value is 1 if the following property is missing
#batchJob.huEdenred.reconciliate.input.file.linesToSkip = 1

#Path where an issuer's response file should be downloaded to (relative, if not exist, will be created)
#Path is relative to batch.job.base.path
#Required
batchJob.huEdenred.reconciliate.input.path = huEdenredJob/reco/input

#Path where a processed (i.e. already reconciliated) files should be moved to (relative, if not exist, will be created)
#Path is relative to batch.job.base.path
#Required
batchJob.huEdenred.reconciliate.output.path = huEdenredJob/reco/output

#Max Repeat Count to reconciliate file (i.e. repeated calls will be applied in a case of an error in a processing flow only)
#Counter will be reset after an application restart
#Possible values: Integer.MAX_VALUE
#Default value (if configuration is missing) is 1 = i.e. accept only one
batchJob.huEdenred.reconciliate.maxRepeatCount = 5

#Whether a reconciliate job should fail in a case of receiving voucher, that is missing in the our DB
#If set to true, only exception is thrown
#If set to false (default value), error message will be added to a error result file
#Default value (if configuration is missing) is false
#batchJob.huEdenred.reconciliate.failOnMissingVoucher = true

#Regular expression which file names should be downloaded from a remote server and reconciliated
#Default value (if configuration is missing) is (.*\.result$)|(.*\.result.zip$)
#batchJob.huEdenred.reconciliate.filename.matcher=(.*\.result$)|(.*\.result.zip$)

##HU Edenred SFTP server setting, all properties are required
#Whether a result report file should be uploaded to sftp server as a part of a generation phase
#Possible values: true/false
#Default value (if configuration is missing is true)
batchJob.huEdenred.sftp.upload.enabled=false
#name, or the IP address, of the server you want to connect to
batchJob.huEdenred.sftp.host=127.0.0.1
#specify which port number on the server to connect to
batchJob.huEdenred.sftp.serverPort=22
#your account name on the server
batchJob.huEdenred.sftp.username=ovv
#password associated with the account.
#batchJob.huEdenred.sftp.password=test
#Specify a key associated with the account (instead of password)
#Prefix file:// is required in all cases, linux link '~/' to home folder cannot be used
#Expected format is a private key in openssh format, e.g. the same as ~/.ssh/id_rsa
#if you do not want to use key file, comment the following line
batchJob.huEdenred.sftp.private.keyfile=file:///home/<USER>/.ssh/id_rsa
#Specify a passphrase to a key associated with the account (instead of password)
batchJob.huEdenred.sftp.passphrase=ovvbatch
#Whether a file should be zipped before uploading to SFTP
#Default value = false (CSV file will be uploaded)
#True means an input CSV file will be zipped and upoaded to SFTP instead of CSV file
batchJob.huEdenred.sftp.compressFile.enabled = false
#Directory to upload local files (related to a batch generation phase)
#Default value is: /
batchJob.huEdenred.sftp.remoteDirectory.upload = sftp/huEdenred
#Remote directory to download files (related to a batch reconciliation phase)
#Default value is: /
batchJob.huEdenred.sftp.remoteDirectory.download = sftp/huEdenred
#Remote directory to archive files (after successfully downloading)
#Default value is: /archive
batchJob.huEdenred.sftp.remoteDirectory.archive = sftp/huEdenred/archive
