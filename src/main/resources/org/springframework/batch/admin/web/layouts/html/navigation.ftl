<#assign home_url><@spring.url relativeUrl="${servletPath}/"/></#assign>
<#assign company_url><@spring.messageText code="company.url" text=companyUrl!"http://www.spring.io"/></#assign>
<#assign company_name><@spring.messageText code="company.name" text=companyName!"Spring"/></#assign>
<#assign product_url><@spring.messageText code="product.url" text=productUrl!"http://projects.spring.io/spring-batch/"/></#assign>
<#assign product_name><@spring.messageText code="product.name" text=productName!"Spring Batch"/></#assign>
<#assign security=JspTaglibs["http://www.springframework.org/security/tags"] />


<div id="primary-navigation">
    <div id="primary-left">
        <ul>
            <#list menuManager.menus as menu>
             <#if menu_index != 3>
                 <#if menu_index == 0>
                    <#--Home should stay simple-->
                     <#assign menu_url><@spring.url relativeUrl="${menu.url}"/></#assign>
                     <li><a href="${menu_url}">${menu.label}</a></li>
                 <#else>
                     <#assign menu_url><@spring.url relativeUrl="${menu.url}?pageSize=100"/></#assign>
                     <li><a href="${menu_url}">${menu.label}</a></li>
                 </#if>
             </#if>
           </#list>
        </ul>
    </div>
    <div id="primary-right">
        <ul>
            <li><a href="${company_url}">${company_name}</a></li>
            <li><a href="${product_url}">${product_name}</a></li>
            <@security.authorize access="isAuthenticated()">
            <li><a href="#" onclick="document.getElementById('logout-form').submit();">Logout</a></li>
            </@security.authorize>
        </ul>
    </div>
    <form id="logout-form" action="<@spring.url relativeUrl="/j_spring_security_logout"/>" method="post">
        <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
    </form>
</div><!-- /primary-navigation -->
