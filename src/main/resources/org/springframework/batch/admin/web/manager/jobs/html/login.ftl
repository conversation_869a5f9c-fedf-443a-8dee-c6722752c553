<#import "/spring.ftl" as spring />



<h1>Login to OVV Batch</h1>

<div id="login-box">

    <h2>Login with Username and Password</h2>

    <#if error??>
     <div class="error">${error}</div>
    </#if>

     <#if msg??>
      <div class="error">${msg}</div>
    </#if>

    <form action="j_spring_security_check" method="post">
        <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
        <label for="username">Username</label>
        <input type="text" id="username" name="username"><br />
        <label for="password">Password</label>
        <input
            type="password" id="password" name="password"><br />
        <input type="submit" value="Login!">
    </form>
</div>

