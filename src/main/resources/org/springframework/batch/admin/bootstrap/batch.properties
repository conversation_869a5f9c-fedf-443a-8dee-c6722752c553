# Common properties for bootstrapping placeholders in an application context.
# Users override these values by providing classpath:/batch-default.properties
# or classpath:/batch-${ENVIRONMENT}.properties

# Properties that are not database specific, or have sensible 
# platform independent defaults:
batch.jdbc.testWhileIdle=false
batch.jdbc.validationQuery=
batch.data.source.init=true
batch.job.configuration.file.dir=target/config
batch.job.service.reaper.interval=60000
