package cz.wincor.ovv.batch.listener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;

/**
 * Implementation of {@link JobExecutionListener} to set logback MDC context
 * <AUTHOR>
 *
 */
public class LogbackJobExecutionListener implements JobExecutionListener {

    private static final Logger LOG = LoggerFactory.getLogger(LogbackJobExecutionListener.class);

    private static final String JOB_NAME_KEY = "jobName";

    @Override
    public void beforeJob(JobExecution jobExecution) {
        MDC.put(JOB_NAME_KEY, jobExecution.getJobInstance().getJobName());
        LOG.debug("MDC filter {} seto to value {}", JOB_NAME_KEY, jobExecution.getJobInstance().getJobName());
    }

    @Override
    public void afterJob(JobExecution jobExecution) {
        MDC.remove(JOB_NAME_KEY);
        LOG.debug("MDC filter {} removed.", JOB_NAME_KEY);
    }

}
