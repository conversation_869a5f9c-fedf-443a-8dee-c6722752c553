package cz.wincor.ovv.batch.listener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.admin.service.JobService;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.NoSuchJobException;

import jakarta.annotation.PostConstruct;

import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * General job execution listener, mainly for logging important
 * information about current run of a job and rescheduling if failed
 *
 * <AUTHOR>
 */
public class CustomJobListener implements JobExecutionListener {

    private static final Logger LOG = LoggerFactory.getLogger(CustomJobListener.class);
    public static final TimeUnit RESCHEDULE_TIME_UNIT = TimeUnit.HOURS;
    public static final String DELAYS_DEIMITER = ";";
    private long maxRepeatCount;
    private boolean rescheduleEnabled;
    private JobService jobService;
    private ScheduledExecutorService scheduler;
    private String delays;
    private int[] finalDelays;


    @PostConstruct
    public void initDelays() {
        if (maxRepeatCount == 0) {
            return;
        }
        if (delays == null) {
            throw new IllegalArgumentException("Delays for rescheduling are not set");
        }
        String[] delaysInArray = delays.split(DELAYS_DEIMITER);
        finalDelays = Arrays.stream(delaysInArray).mapToInt(Integer::parseInt).toArray();
        if (finalDelays.length != (maxRepeatCount)) {
            throw new IllegalArgumentException("Max repeat count is not same as count of delay's definitions.");
        }
    }
    
    @Override
    public void beforeJob(JobExecution jobExecution) {
        LOG.info("{} job started at: {}", jobExecution.getJobInstance().getJobName(), jobExecution.getStartTime());
    }

    @Override
    public void afterJob(JobExecution jobExecution) {
        LOG.info("{} job stopped at : {}", jobExecution.getJobInstance().getJobName(), jobExecution.getEndTime());
        long millis = ChronoUnit.MILLIS.between(jobExecution.getStartTime(), jobExecution.getEndTime());
        LOG.info("{} job took in millis {} ({})", jobExecution.getJobInstance().getJobName(), millis,
                String.format("%02d min, %02d sec", TimeUnit.MILLISECONDS.toMinutes(millis),
                        TimeUnit.MILLISECONDS.toSeconds(millis)
                                - TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(millis))));

        if (jobExecution.getStatus() == BatchStatus.COMPLETED) {
            LOG.info("{} job completed successfully", jobExecution.getJobInstance().getJobName());
            // Here you can perform some other business logic like cleanup
        } else if (jobExecution.getStatus() == BatchStatus.FAILED) {
            LOG.info("{} job failed with following exceptions:", jobExecution.getJobInstance().getJobName());
            List<Throwable> exceptionList = jobExecution.getAllFailureExceptions();
            for (Throwable th : exceptionList) {
                LOG.info("    Exception: {}", th.getLocalizedMessage());
            }
            if (rescheduleEnabled) {
                rescheduleJob(jobExecution);
            } else {
                LOG.warn("Job {} failed, but rescheduling is disabled.", jobExecution.getJobInstance().getJobName());
            }
        }
    }

    /**
     * Reschedule restart of the current job execution
     * @param jobExecution
     */
    private void rescheduleJob(JobExecution jobExecution) {
        final JobParameters jobParameters = jobExecution.getJobParameters();
        int executionCount;
        try {
            executionCount = jobService.getJobExecutionsForJobInstance(jobExecution.getJobInstance().getJobName(), jobExecution.getJobInstance().getInstanceId()).size();
            LOG.info("Job instance {} related to job {} has {} execution count.",
                    jobExecution.getJobInstance().getId(), jobExecution.getJobInstance().getJobName(), executionCount);
        } catch (NoSuchJobException e) {
            LOG.error("No such job " + jobExecution.getJobInstance().getJobName() + ", rescheduling is not possible.", e);
            return;
        }
        if (executionCount > maxRepeatCount) {
            LOG.error("Job instance id {} related to job {} exceeded max execution count {}, no more reschedule possible.",
                    jobExecution.getJobInstance().getId(), jobExecution.getJobInstance().getJobName(), maxRepeatCount);
            return;
        }

        long nextFire = finalDelays[executionCount - 1];
        scheduler.schedule(new Runnable() {
            @Override
            public void run() {
                try {
                    jobService.restart(jobExecution.getId());
                } catch (Exception e) {
                    LOG.error("Exception when trying to automatically restarting jobExecutionId " + jobExecution.getId()
                            + " related to job " + jobExecution.getJobInstance().getJobName(), e);
                }
            }
        }, nextFire, RESCHEDULE_TIME_UNIT);
        LOG.info("Job {} rescheduled in {} {} from now.", jobExecution.getJobInstance().getJobName(), nextFire, RESCHEDULE_TIME_UNIT);
    }

    public CustomJobListener setJobService(JobService jobService) {
        this.jobService = jobService;
        return this;
    }

    public CustomJobListener setRescheduleEnabled(boolean rescheduleEnabled) {
        this.rescheduleEnabled = rescheduleEnabled;
        return this;
    }

    public CustomJobListener setScheduler(ScheduledExecutorService scheduler) {
        this.scheduler = scheduler;
        return this;
    }

    public void setMaxRepeatCount(long maxRepeatCount) {
        this.maxRepeatCount = maxRepeatCount;
    }

    public void setDelays(String delays) {
        this.delays = delays;
    }

}
