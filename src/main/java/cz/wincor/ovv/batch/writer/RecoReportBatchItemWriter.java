package cz.wincor.ovv.batch.writer;

import static cz.wincor.ovv.batch.BatchConstants.PARAM_FAIL_ON_MISSING;
import static cz.wincor.ovv.batch.BatchConstants.PARAM_RECO_ERROR_FILE;
import static cz.wincor.ovv.batch.BatchConstants.PARAM_REPORT_FILE;
import static cz.wincor.ovv.batch.BatchConstants.TRUE;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Date;

import cz.wincor.ovv.batch.service.ReportBatchService;
import cz.wincor.ovv.batch.service.ReportFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionException;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.support.AbstractItemStreamItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.entity.batch.ReportFile;
import cz.wincor.ovv.batch.pojo.VoucherResponse;

/**
 * Reconciliation batch item writer is responsible for writing reconciliation
 * result from issuer into DB
 *
 * <AUTHOR>
 *
 */
@Component("recoReportBatchItemWriter")
@Scope("step")
public class RecoReportBatchItemWriter extends AbstractItemStreamItemWriter<VoucherResponse>
        implements StepExecutionListener {

    private static final Logger LOG = LoggerFactory.getLogger(RecoReportBatchItemWriter.class);

    @Autowired
    private ReportBatchService reportBatchService;

    @Autowired
    private ReportFileService reportFileService;

    private JobExecution jobExecution;

    private ReportFile reportFile;

    private boolean failOnMissingVoucher;


    @Override
    public void write(Chunk<? extends VoucherResponse> paramList) throws Exception {
        for (VoucherResponse voucherResponse : paramList) {
            int updatedRows = reportBatchService.setResultToReportBatch(reportFile.getId(), voucherResponse.getResultCode(),
                    voucherResponse.getResultDescription(), voucherResponse.getVoucherNumber());
            // Usually updated only one row, but for case of reversals responses, it may affect multiple rows
            if (updatedRows > 1) {
                LOG.warn("Response {} affected multiple ({}) rows.", voucherResponse, updatedRows);
            }
            if (updatedRows == 0) {
                if (failOnMissingVoucher) {
                    throw new JobExecutionException("Reconciliation file contains unknown voucher number: " + voucherResponse.getVoucherNumber());
                } else {
                    String message = "Voucher " + voucherResponse.getVoucherNumber() + " not found in db.";
                    addErrorToFile(message);
                    LOG.debug("Error added to a error file: {}", message);
                }
            }
        }
    }

    /**
     * Add an input message error to a error report file
     * @param message
     * @throws IOException
     */
    protected synchronized void addErrorToFile(String message) throws IOException {
        if (jobExecution.getExecutionContext().containsKey(PARAM_RECO_ERROR_FILE)) {
            File errorFile = (File)jobExecution.getExecutionContext().get(PARAM_RECO_ERROR_FILE);
            Files.write(errorFile.toPath(), (message + "\n").getBytes(BatchConstants.CHARSET_UTF8), StandardOpenOption.APPEND, StandardOpenOption.SYNC);
        } else {
            Path path = Paths.get(reportFile.getFilePath() + "." + BatchConstants.PARAM_ERR_SUFFIX);
            Files.write(path, (message + "\n").getBytes(BatchConstants.CHARSET_UTF8), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING, StandardOpenOption.SYNC);
            jobExecution.getExecutionContext().put(PARAM_RECO_ERROR_FILE, path.toFile());
        }
    }

    public synchronized void beforeStep(StepExecution stepExecution) {
        jobExecution = stepExecution.getJobExecution();
        reportFile = (ReportFile) jobExecution.getExecutionContext().get(PARAM_REPORT_FILE);
        String failOnMissingVoucherValue = jobExecution.getJobParameters().getString(PARAM_FAIL_ON_MISSING);
        failOnMissingVoucher =  failOnMissingVoucherValue != null ? TRUE.equalsIgnoreCase(failOnMissingVoucherValue) : false;
    }

    /**
     * Update report file with information about the current reconciliation
     */
    @Override
    public synchronized ExitStatus afterStep(StepExecution arg0) {
        reportFile.setReconciliated(new Date());
        reportFile.setRecoJobExecutionId(jobExecution.getId());
        reportFileService.save(reportFile);
        if (jobExecution.getExecutionContext().containsKey(PARAM_RECO_ERROR_FILE)) {
            jobExecution.getExecutionContext().put(BatchConstants.PARAM_FILE_TO_UPLOAD, ((File)jobExecution.getExecutionContext().get(PARAM_RECO_ERROR_FILE)).getAbsolutePath());
        }
        return null;
    }

    public void setFailOnMissingVoucher(boolean failOnMissingVoucher) {
        this.failOnMissingVoucher = failOnMissingVoucher;
    }

}
