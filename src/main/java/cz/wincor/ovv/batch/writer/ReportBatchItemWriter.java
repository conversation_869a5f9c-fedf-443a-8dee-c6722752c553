package cz.wincor.ovv.batch.writer;

import java.util.ArrayList;
import java.util.List;

import cz.wincor.ovv.batch.service.ReportBatchService;
import cz.wincor.ovv.batch.service.ReportFileService;
import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionException;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.support.AbstractItemStreamItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.entity.batch.ReportBatch;
import cz.wincor.ovv.batch.entity.batch.ReportFile;
import cz.wincor.ovv.batch.pojo.Voucher;

@Component("reportBatchItemWriter")
@Scope("step")
public class ReportBatchItemWriter extends AbstractItemStreamItemWriter<Voucher> implements StepExecutionListener {

    private static final Logger LOG = LoggerFactory.getLogger(ReportBatchItemWriter.class);

    @Autowired
    private ReportBatchService reportBatchService;

    @Autowired
    private ReportFileService reportFileService;

    private JobExecution jobExecution;

    @Override
    public void write(Chunk<? extends Voucher> chunk) throws Exception {
        ReportFile reportFile = (ReportFile) jobExecution.getExecutionContext().get(BatchConstants.PARAM_REPORT_FILE);
        List<ReportBatch> batchesToSave = new ArrayList<>();
        for (Voucher voucher : chunk) {
            if (LOG.isTraceEnabled()) {
                LOG.trace("Writing voucher {}", voucher);
            }
            ReportBatch reportBatch = new ReportBatch();
            reportBatch.setReportFile(reportFile);
            reportBatch.setVoucherNumber(voucher.getVoucherNumber());
            reportBatch.setOvvTransactionRequestId(voucher.getId());
            Validate.notNull(voucher.getOvvStore(), "Related store is required.");
            reportBatch.setOvvStoreId(voucher.getOvvStore().getId());
            batchesToSave.add(reportBatch);
        }

        // Save all ReportBatches in one transaction
        List<ReportBatch> savedBatches = reportBatchService.saveAll(batchesToSave);
        if (savedBatches.size() != batchesToSave.size()) {
            throw new JobExecutionException(
                    "Unexpected result from saving batches into DB, expected batches to be saved: "
                            + batchesToSave.size() + ", but actual " + savedBatches.size());
        }
        LOG.info("{} report batch items successfully saved into DB.", savedBatches.size());
    }

    public void beforeStep(StepExecution stepExecution) {
        jobExecution = stepExecution.getJobExecution();
    }

    @Override
    public ExitStatus afterStep(StepExecution step) {
        ReportFile reportFile = (ReportFile) jobExecution.getExecutionContext().get(BatchConstants.PARAM_REPORT_FILE);
        jobExecution.getExecutionContext().put(BatchConstants.PARAM_FILE_TO_UPLOAD, reportFile.getFilePath());
        LOG.info("Number of all items written to file: {}.", step.getWriteCount());
        if (step.getWriteCount() > Integer.MAX_VALUE) {
            LOG.error("Invalid writeCount value: '{}'.", step.getWriteCount());
            return ExitStatus.FAILED;
        }
        // Expected rows need to be sum over all restarts
        reportFile.setItemsCount(reportFile.getItemsCount() + (int) step.getWriteCount());
        reportFileService.save(reportFile);
        LOG.info("Batch file should contain now: {} vouchers (i.e. rows).", reportFile.getItemsCount());
        return null;
    }

}
