package cz.wincor.ovv.batch.reader;

import org.springframework.batch.item.ParseException;
import org.springframework.batch.item.UnexpectedInputException;
import org.springframework.batch.item.file.FlatFileItemReader;

/**
 * Thread safe wrapper for {@link FlatFileItemReader}
 * <AUTHOR>
 *
 */
public class SynchronizedFlatFileItemReader<T> extends FlatFileItemReader<T> {

    @Override
    public synchronized T read() throws Exception, UnexpectedInputException, ParseException {
        return super.read();
    }
}
