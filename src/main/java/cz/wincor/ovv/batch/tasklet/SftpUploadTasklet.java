package cz.wincor.ovv.batch.tasklet;

import java.io.File;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.JobExecutionException;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.gateway.UploadToSftpGateway;

/**
 * Sftp uploader tasklet
 *
 * <AUTHOR>
 *
 */
public class SftpUploadTasklet implements Tasklet {

    private static final Logger LOG = LoggerFactory.getLogger(SftpUploadTasklet.class);
    private UploadToSftpGateway uploadGateway;
    private boolean enabled;

    /**
     * Try to upload a result file to SFTP server (through the sftp channel)
     */
    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        if (!chunkContext.getStepContext().getStepExecution().getJobExecution()
                .getExecutionContext().containsKey(BatchConstants.PARAM_FILE_TO_UPLOAD)) {
            LOG.info("No file to upload.");
            return RepeatStatus.FINISHED;
        }
        String fileToUploadPath = (String) chunkContext.getStepContext().getStepExecution().getJobExecution()
                .getExecutionContext().get(BatchConstants.PARAM_FILE_TO_UPLOAD);

        if (fileToUploadPath == null || fileToUploadPath.isEmpty()) {
            throw new JobExecutionException("Path to file to upload is null or empty, so no file cannot be uploaded.");
        }

        if (!enabled) {
            LOG.info("SFTP upload of file {} is disabled.", fileToUploadPath);
            return RepeatStatus.FINISHED;
        }

        File file = new File(fileToUploadPath);
        if (file.exists()) {
            try {
                uploadGateway.uploadFile(file);
                LOG.info("File {} was successfully uploaded to SFTP server.", file.getAbsolutePath());
            } catch (Exception e) {
                throw new JobExecutionException("Error during upload to SFTP server: " + e.getMessage(), e);
            }
        } else {
            throw new JobExecutionException(
                    "File " + file.getAbsolutePath() + " does not exist and cannot be uploaded to sftp server.");
        }
        return RepeatStatus.FINISHED;
    }

    public void setUploadGateway(UploadToSftpGateway uploadGateway) {
        this.uploadGateway = uploadGateway;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
