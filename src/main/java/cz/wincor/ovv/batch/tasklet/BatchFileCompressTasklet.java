package cz.wincor.ovv.batch.tasklet;

import java.nio.file.Files;
import java.nio.file.Paths;

import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.entity.batch.ReportFile;
import cz.wincor.ovv.batch.util.ZipUtil;

/**
 * This tasklet is responsible for compressing batch file into ZIP format
 * beginning of a file
 *
 * <AUTHOR>
 *
 */
public class BatchFileCompressTasklet implements Tasklet {

    private boolean compressingEnabled;

    private static final Logger LOG = LoggerFactory.getLogger(BatchFileCompressTasklet.class);

    @Override
    public RepeatStatus execute(StepContribution paramStepContribution, ChunkContext paramChunkContext)
            throws Exception {
        if (!compressingEnabled) {
            LOG.info("Compressing to ZIP file is disabled.");
            return RepeatStatus.FINISHED;
        }
        // get required parameters from context and validate it
        ReportFile reportFile = (ReportFile) paramChunkContext.getStepContext().getStepExecution().getJobExecution()
                .getExecutionContext().get(BatchConstants.PARAM_REPORT_FILE);
        Validate.isTrue(Files.exists(Paths.get(reportFile.getFilePath())),
                "File " + reportFile.getFilePath() + " must exist before adding number of all items.");

        // To insert at the beginning of file, we need to create a temp file and
        // copy content from old file
        String zipFile = reportFile.getFilePath() + ".zip";

        //Delete previous zipped file (if any exists)
        if (Files.deleteIfExists(Paths.get(zipFile))) {
            LOG.debug("Previous zip {} file was deleted, it will be created a new file.", zipFile);
        }

        ZipUtil.pack(zipFile, reportFile.getFilePath());

        LOG.info("File {} successfully compress into ZIP file {}.", reportFile.getFileName(), zipFile);
        //Add path to file into a context to be uploaded later
        paramChunkContext.getStepContext().getStepExecution().getJobExecution().getExecutionContext()
                .put(BatchConstants.PARAM_FILE_TO_UPLOAD, zipFile);
        return RepeatStatus.FINISHED;
    }

    public void setCompressingEnabled(boolean compressingEnabled) {
        this.compressingEnabled = compressingEnabled;
    }

}
