package cz.wincor.ovv.batch.tasklet;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.JobExecutionException;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.util.ZipUtil;

/**
 * This tasklet is responsible for unzipping of incoming input file (if needed)
 *
 * <AUTHOR>
 *
 */
@Component("recoFileExtractTasklet")
public class RecoFileExtractTasklet implements Tasklet {

    private static final Logger LOG = LoggerFactory.getLogger(RecoFileExtractTasklet.class);

    @Override
    public RepeatStatus execute(StepContribution paramStepContribution, ChunkContext paramChunkContext)
            throws Exception {
        String inputFilePath = (String) paramChunkContext.getStepContext().getJobParameters()
                .get(BatchConstants.PARAM_INPUT_FILENAME);
        Validate.notEmpty(inputFilePath, "Input file path must be defined.");
        if (!FilenameUtils.isExtension(inputFilePath, new String[] { "zip", "ZIP" })) {
            LOG.info("Incoming file does not have a 'zip' extension, so it do not need to be unzipped.");
            paramChunkContext.getStepContext().getStepExecution().getJobExecution().getExecutionContext()
            .put(BatchConstants.PARAM_INPUT_FILENAME, inputFilePath);
            return RepeatStatus.FINISHED;
        }

        // Get directory, e.g. C:\Users\<USER>\ovv-batch\reconciliate.archive\
        String directoryToExtract = FilenameUtils.getFullPath(inputFilePath);
        // Get basefile name (and remove zip extension), e.g.:
        // WN_CZ_TVM_20160825_1460713440000.csv
        String expectedRecoFilename = FilenameUtils.getBaseName(inputFilePath);

        if (Files.deleteIfExists(Paths.get(directoryToExtract, expectedRecoFilename))) {
            LOG.info("File {} deleted before extracting a zip archive.", expectedRecoFilename);
        }
        List<String> extractedFiles = ZipUtil.unpack(inputFilePath, directoryToExtract);
        if (extractedFiles.size() == 0) {
            throw new JobExecutionException("No file was extracted from the archive: " + inputFilePath);
        }
        if (extractedFiles.size() > 1) {
            LOG.warn("Exptecting only one file in archive, but was {}: {}", extractedFiles.size(),
                    Arrays.toString(extractedFiles.toArray()));
        }

        boolean updatedInputFile = false;
        for (String extractedFile : extractedFiles) {
            if (expectedRecoFilename.equals(FilenameUtils.getName(extractedFile))) {
                paramChunkContext.getStepContext().getStepExecution().getJobExecution().getExecutionContext()
                        .put(BatchConstants.PARAM_INPUT_FILENAME, extractedFile);
                updatedInputFile = true;
            }
        }
        if (!updatedInputFile) {
            throw new JobExecutionException("Exctracted files does not contain exptected filename " + expectedRecoFilename);
        }
        //Test whether extracted file exist on a defined path
        Path extractedFile = Paths.get(paramChunkContext.getStepContext().getStepExecution().getJobExecution().getExecutionContext()
                .getString(BatchConstants.PARAM_INPUT_FILENAME));
        if (!Files.exists(extractedFile)) {
            throw new JobExecutionException("Extracted file does not exist: " + extractedFile.toAbsolutePath().toString());
        }
        LOG.info("File {} was successfully extracted.", extractedFile.toAbsolutePath().toString());
        return RepeatStatus.FINISHED;
    }

}
