package cz.wincor.ovv.batch.tasklet;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.JobExecutionException;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.integration.file.FileHeaders;
import org.springframework.messaging.Message;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.gateway.ArchiveSftpGateway;

/**
 * Sftp archiver tasklet
 *
 * <AUTHOR>
 *
 */
public class SftpArchiveTasklet implements Tasklet {

    private static final Logger LOG = LoggerFactory.getLogger(SftpArchiveTasklet.class);
    private ArchiveSftpGateway archiveGateway;
    private boolean enabled;

    /**
     * Try to upload a result file to SFTP server (through the sftp channel)
     */
    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        String inputFilePath = (String) chunkContext.getStepContext().getJobParameters()
                .get(BatchConstants.PARAM_INPUT_FILENAME);
        Validate.notEmpty(inputFilePath, "Input file path must be defined.");

        if (!enabled) {
            LOG.info("SFTP archivation of file {} is disabled.", inputFilePath);
            return RepeatStatus.FINISHED;
        }

        // Get file name (i.e. extract path)
        String remoteFilename = FilenameUtils.getName(inputFilePath);

        try {
            Message<Boolean> response = archiveGateway.archiveFile(remoteFilename);
            if (!Boolean.TRUE.equals(response.getPayload())) {
                throw new JobExecutionException("Unexpected response from the SFTP server, archiving probably failed.");
            }
            LOG.info("File {} was successfully archived to {}", response.getHeaders().get(FileHeaders.REMOTE_FILE),
                    response.getHeaders().get(FileHeaders.RENAME_TO));
        } catch (Exception e) {
            Throwable cause = e.getCause();
            while (cause != null) {
                if (cause.getMessage() != null && cause.getMessage().contains("2: No such file")) {
                    LOG.warn("Remote file {} cannot be moved to archive folder, it does not exist.", remoteFilename);
                    return RepeatStatus.FINISHED;
                }
                cause = cause.getCause();
            }
            throw new JobExecutionException("Error during archiving on a remote SFTP server: " + e.getMessage(), e);
        }
        return RepeatStatus.FINISHED;
    }

    public void setArchiveGateway(ArchiveSftpGateway archiveGateway) {
        this.archiveGateway = archiveGateway;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
