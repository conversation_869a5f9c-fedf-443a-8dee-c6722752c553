package cz.wincor.ovv.batch.tasklet;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

/**
 * Dummy tasklet, doing nothing
 * <AUTHOR>
 *
 */
@Component("dummyTasklet")
public class DummyTasklet implements Tasklet   {

    private static final Logger LOG = LoggerFactory.getLogger(DummyTasklet.class);

    @Override
    public RepeatStatus execute(StepContribution paramStepContribution, ChunkContext paramChunkContext)
            throws Exception {
        LOG.info("Dummy tasklet called.");
        return RepeatStatus.FINISHED;
    }


}
