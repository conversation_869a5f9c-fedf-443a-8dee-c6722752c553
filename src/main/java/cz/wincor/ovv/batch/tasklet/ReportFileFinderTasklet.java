package cz.wincor.ovv.batch.tasklet;

import java.io.File;

import cz.wincor.ovv.batch.service.ReportFileService;
import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.entity.batch.ReportFile;
import cz.wincor.ovv.batch.util.FilenameHelper;

/**
 * This Tasklet is responsible for searching for a related {@link ReportFile}
 * saved in DB
 *
 * <AUTHOR>
 *
 */
@Component("reportFileFinderTasklet")
public class ReportFileFinderTasklet implements Tasklet {

    private static final Logger LOG = LoggerFactory.getLogger(ReportFileFinderTasklet.class);

    @Autowired
    private ReportFileService reportFileService;

    @Override
    public RepeatStatus execute(StepContribution paramStepContribution, ChunkContext paramChunkContext)
            throws Exception {
        String inputFilePath = (String) paramChunkContext.getStepContext().getJobExecutionContext()
                .get(BatchConstants.PARAM_INPUT_FILENAME);
        Validate.notEmpty(inputFilePath, "Input file path must be defined.");
        File inputFile = new File(inputFilePath);
        Validate.isTrue(inputFile.exists(), "Input file " + inputFilePath + " does not exist or cannot be read.");
        LOG.info("Trying to run reconciliation job for incoming file: {}", inputFilePath);
        FilenameHelper filenameHelper = FilenameHelper.parseFromString(inputFile.getName());
        ReportFile reportFile = reportFileService.findByUuidAndOvvIssuerIgnoreCase(
                filenameHelper.getUuid(), filenameHelper.getIssuer());
        Validate.notNull(reportFile, "Related report file not found in DB.");
        paramChunkContext.getStepContext().getStepExecution().getJobExecution().getExecutionContext().put(BatchConstants.PARAM_REPORT_FILE,
                reportFile);
        return RepeatStatus.FINISHED;
    }
}
