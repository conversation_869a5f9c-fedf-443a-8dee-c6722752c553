package cz.wincor.ovv.batch.tasklet;

import static cz.wincor.ovv.batch.BatchConstants.PARAM_CSV_SUFFIX;
import static cz.wincor.ovv.batch.BatchConstants.PARAM_OUTPUT_FILENAME_COUNTRY;
import static cz.wincor.ovv.batch.BatchConstants.PARAM_OUTPUT_PATH;
import static cz.wincor.ovv.batch.BatchConstants.PARAM_OVV_ISSUER;
import static cz.wincor.ovv.batch.BatchConstants.PARAM_REPORT_FILE;
import static cz.wincor.ovv.batch.BatchConstants.PARAM_REPORT_START_DATE;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;

import cz.wincor.ovv.batch.service.ReportFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobInstance;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.explore.JobExplorer;
import org.springframework.batch.core.explore.support.JobExplorerFactoryBean;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cz.wincor.ovv.batch.entity.batch.ReportFile;
import cz.wincor.ovv.batch.entity.ovv.CountryCode;
import cz.wincor.ovv.batch.util.FilenameHelper;

/**
 * OVV output filename generator
 *
 * <AUTHOR>
 *
 */
@Component("ovvOutputFileGenerator")
public class OvvOutputFileGenerator implements Tasklet {

    private static final Logger LOG = LoggerFactory.getLogger(OvvOutputFileGenerator.class);

    @Autowired
    private ReportFileService reportFileService;

    @Autowired
    private JobExplorerFactoryBean jobExplorerFactory;

    /**
     * Generate OVV output filename from input {@link JobParameters}
     *
     * @param jobParameters
     * @return
     * @throws Exception
     */
    @Override
    public RepeatStatus execute(StepContribution paramStepContribution, ChunkContext paramChunkContext)
            throws Exception {
        ReportFile reportFile = getLastReportFileIfRestarted(paramChunkContext.getStepContext().getStepExecution()
                .getJobExecution().getJobInstance().getInstanceId());
        JobParameters jobParameters = paramChunkContext.getStepContext().getStepExecution().getJobParameters();
        if (reportFile == null) {
            FilenameHelper filenameHelper = new FilenameHelper();
            filenameHelper.setIssuer(jobParameters.getString(PARAM_OVV_ISSUER));
            filenameHelper.setCountry(jobParameters.getString(PARAM_OUTPUT_FILENAME_COUNTRY));
            filenameHelper.setReportDate(jobParameters.getString(PARAM_REPORT_START_DATE));
            filenameHelper.setSuffix1(PARAM_CSV_SUFFIX);
            filenameHelper.setUuid(
                    paramChunkContext
                            .getStepContext()
                            .getStepExecution()
                            .getJobExecution()
                            .getStartTime()
                            .atOffset(ZoneOffset.UTC)
                            .toInstant()
                            .toEpochMilli()
            );

            reportFile = new ReportFile();
            reportFile.setFileName(filenameHelper.toString());
            reportFile.setFilePath(jobParameters.getString(PARAM_OUTPUT_PATH) + "/" + reportFile.getFileName());
            reportFile.setOvvIssuer(jobParameters.getString(PARAM_OVV_ISSUER));
            reportFile.setFileNameDate(jobParameters.getString(PARAM_REPORT_START_DATE));
            reportFile.setUuid(
                    paramChunkContext
                            .getStepContext()
                            .getStepExecution()
                            .getJobExecution()
                            .getStartTime()
                            .atOffset(ZoneOffset.UTC)
                            .toInstant()
                            .toEpochMilli()
            );
            reportFile
                    .setJobExecutionId(paramChunkContext.getStepContext().getStepExecution().getJobExecution().getId());
            reportFile.setCreated(new Date());
            reportFile.setItemsCount(0);
            reportFile.setCountryCode(CountryCode.fromValue(jobParameters.getString(PARAM_OUTPUT_FILENAME_COUNTRY)));
        } else {
            reportFile
                    .setJobExecutionId(paramChunkContext.getStepContext().getStepExecution().getJobExecution().getId());
        }
        Path reportFilePath = Paths.get(reportFile.getFilePath());
        if (!Files.exists(reportFilePath)) {
            Files.createDirectories(Paths.get(jobParameters.getString(PARAM_OUTPUT_PATH)));
            Files.createFile(reportFilePath);
        }
        LOG.info("Job with instance id {} is trying to write to file {}.",
                paramChunkContext.getStepContext().getStepExecution().getJobExecution().getJobInstance(),
                reportFile.getFilePath());
        paramChunkContext.getStepContext().getStepExecution().getJobExecution().getExecutionContext()
                .put(PARAM_REPORT_FILE, reportFile);
        reportFileService.save(reportFile);
        LOG.info("Report file {} successfully saved into DB", reportFile);
        return RepeatStatus.FINISHED;
    }

    private ReportFile getLastReportFileIfRestarted(long jobInstanceId) throws Exception {
        JobExplorer jobExplorer = jobExplorerFactory.getObject();
        JobInstance jobInstance = jobExplorer.getJobInstance(jobInstanceId);

        List<JobExecution> jobExecutions = jobExplorer.getJobExecutions(jobInstance);
        for (JobExecution jobExecution : jobExecutions) {
            if (!jobExecution.getExitStatus().equals(ExitStatus.UNKNOWN)) {
                // You found a completed job, possible candidate for a restart
                // You may check if the job is restarted comparing jobParameters
                ExecutionContext executionContext = jobExecution.getExecutionContext();
                if (executionContext.containsKey(PARAM_REPORT_FILE)) {
                    ReportFile reportFile = (ReportFile) executionContext.get(PARAM_REPORT_FILE);
                    return reportFileService.getById(reportFile.getId());
                }
            }
        }
        return null;
    }

}
