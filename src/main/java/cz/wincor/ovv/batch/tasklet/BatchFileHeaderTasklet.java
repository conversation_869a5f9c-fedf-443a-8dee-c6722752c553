package cz.wincor.ovv.batch.tasklet;

import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.JobExecutionException;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.entity.batch.ReportFile;
import cz.wincor.ovv.batch.util.FileUtils;

/**
 * This tasklet is responsible for writing number of items (and other defined
 * custom header - if any) in a file at the beginning of a file
 *
 * <AUTHOR>
 *
 */
public class BatchFileHeaderTasklet implements Tasklet {

    private static final String COLUMN_DELIMITER = ";";

    private static final Logger LOG = LoggerFactory.getLogger(BatchFileHeaderTasklet.class);

    private List<String> additionalHeaderValues = new ArrayList<>();

    @Override
    public RepeatStatus execute(StepContribution paramStepContribution, ChunkContext paramChunkContext)
            throws Exception {
        // get required parameters from context and validate it
        ReportFile reportFile = (ReportFile) paramChunkContext.getStepContext().getJobExecutionContext()
                .get(BatchConstants.PARAM_REPORT_FILE);
        Validate.isTrue(Files.exists(Paths.get(reportFile.getFilePath())),
                "File " + reportFile.getFilePath() + " must exist before adding number of all items.");

        LOG.debug("Trying to validate expected number of rows with a real value.");
        int realRowsInFile = FileUtils.countLines(reportFile.getFilePath());
        if (realRowsInFile != reportFile.getItemsCount()) {
            LOG.warn(
                    "This job instance execution is broken, try to run another instance of this job instead (avoid restarting as well).");
            throw new JobExecutionException("Expected number of vouchers is not same as a real value, i.e. expected "
                    + reportFile.getItemsCount() + " vs real " + realRowsInFile);
        }

        // To insert at the beginning of file, we need to create a temp file and
        // copy content from old file
        String tempFile = reportFile.getFilePath() + ".temp";

        try (OutputStream output = Files.newOutputStream(Paths.get(tempFile), StandardOpenOption.CREATE,
                StandardOpenOption.TRUNCATE_EXISTING, StandardOpenOption.SYNC)) {
            PrintWriter printWriter = new PrintWriter(new OutputStreamWriter(output, StandardCharsets.UTF_8), true);
            // Write number of all items at the first line
            printWriter.print(realRowsInFile);
            if (additionalHeaderValues != null && !additionalHeaderValues.isEmpty()) {
                printWriter.print(COLUMN_DELIMITER);
                String additionalHeaders = additionalHeaderValues.stream().collect(Collectors.joining(COLUMN_DELIMITER));
                printWriter.println(additionalHeaders);
            } else {
                printWriter.println();
            }
            // Copy content from the main report file to temp file
            Files.copy(Paths.get(reportFile.getFilePath()), output);
            // Rename temp file to the main report file
            Files.move(Paths.get(tempFile), Paths.get(reportFile.getFilePath()), StandardCopyOption.REPLACE_EXISTING,
                    StandardCopyOption.ATOMIC_MOVE);
            LOG.info("Successfully added number of items ({}) to a batch report file.", realRowsInFile);
        } finally {
            Files.deleteIfExists(Paths.get(tempFile));
        }
        // Check if anything unexpecting did not happen
        if (!Files.exists(Paths.get(reportFile.getFilePath()))) {
            throw new JobExecutionException(
                    "Batch file " + reportFile.getFilePath() + " must exist after a tasklet execution.");
        }
        return RepeatStatus.FINISHED;
    }

    public void setAdditionalHeaderValues(List<String> additionalHeaderValues) {
        this.additionalHeaderValues = additionalHeaderValues;
    }
}
