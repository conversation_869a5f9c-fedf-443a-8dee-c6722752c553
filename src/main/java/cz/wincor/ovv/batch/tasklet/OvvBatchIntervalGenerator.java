package cz.wincor.ovv.batch.tasklet;

import static cz.wincor.ovv.batch.BatchConstants.PARAM_READER_END;
import static cz.wincor.ovv.batch.BatchConstants.PARAM_READER_START;
import static cz.wincor.ovv.batch.BatchConstants.PARAM_RECEIVER_TIMEZONE;
import static cz.wincor.ovv.batch.BatchConstants.PARAM_REPORT_START_DATE;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.pojo.ReportPeriod;

/**
 * OVV output filename generator
 *
 * <AUTHOR>
 *
 */
@Component("ovvBatchIntervalGenerator")
public class OvvBatchIntervalGenerator implements Tasklet {

    private static final Logger LOG = LoggerFactory.getLogger(OvvBatchIntervalGenerator.class);

    /**
     * Generate OVV output filename from input {@link JobParameters}
     *
     * @param jobParameters
     * @return
     * @throws Exception
     */
    @Override
    public RepeatStatus execute(StepContribution paramStepContribution, ChunkContext paramChunkContext)
            throws Exception {
        JobParameters jobParameters = paramChunkContext.getStepContext().getStepExecution().getJobParameters();

        // get the whole day interval extracted from reportStartDate value
        // e.g. reportStartDate is 20170306
        LocalDate requiredDay = LocalDate.parse(jobParameters.getString(PARAM_REPORT_START_DATE), DateTimeFormatter.BASIC_ISO_DATE);
        // -> start date should be equal to 06.02.2017 00:00:00.000
        LocalDateTime startDateTime = LocalDateTime.of(requiredDay, LocalTime.MIDNIGHT);

        ReportPeriod reportPeriod = ReportPeriod.valueOf(jobParameters.getString(BatchConstants.PARAM_REPORT_PERIOD));
        LocalDateTime enDateTime;
        switch (reportPeriod) {
        case DAY:
            // -> end date should be equal to 06.02.2017 23:59:59.999999
            enDateTime = LocalDateTime.of(requiredDay, LocalTime.MAX);
            break;
        case WEEK:
            // -> end date should be equal to 12.12.2011 23:59:59.999999
            enDateTime = startDateTime.plusWeeks(1).minusNanos(1);
            break;
        case MONTH:
            // -> end date should be equal to 03.12.2011 23:59:59.999999
            enDateTime = startDateTime.plusMonths(1).minusNanos(1);
            break;
        default:
            throw new IllegalArgumentException("Unsuppoted report period exception: " + reportPeriod);
        }



        // Set computed fields into a job execution context
        // A required zone id is assigned to start of day 00:00:00.000 and then
        // is converted to a server's zoned date time, because a database
        // contains server's local date time
        Date from = Date.from(startDateTime.atZone(ZoneId.of(jobParameters.getString(PARAM_RECEIVER_TIMEZONE)))
                .withZoneSameInstant(ZoneId.systemDefault()).toInstant());
        paramChunkContext.getStepContext().getStepExecution().getJobExecution().getExecutionContext()
                .put(PARAM_READER_START, from);
        // A required zone id is assigned to end of day 03.12.2011 23:59:59.999
        // and then
        // is converted to a server's zoned date time, because a database
        // contains server's local date time
        Date to = Date.from(enDateTime.atZone(ZoneId.of(jobParameters.getString(PARAM_RECEIVER_TIMEZONE)))
                .withZoneSameInstant(ZoneId.systemDefault()).toInstant());
        paramChunkContext.getStepContext().getStepExecution().getJobExecution().getExecutionContext()
                .put(PARAM_READER_END, to);
        LOG.info("Report {} will be generated with this database parameters: from - {}, to - {}.", paramChunkContext.getStepContext().getJobName(), from, to);
        return RepeatStatus.FINISHED;
    }

}
