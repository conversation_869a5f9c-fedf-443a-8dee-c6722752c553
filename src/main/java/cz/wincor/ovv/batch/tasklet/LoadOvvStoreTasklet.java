package cz.wincor.ovv.batch.tasklet;

import static cz.wincor.ovv.batch.BatchConstants.PARAM_OUTPUT_FILENAME_COUNTRY;
import static cz.wincor.ovv.batch.BatchConstants.PARAM_PARTNER_ID;
import static cz.wincor.ovv.batch.BatchConstants.PARAM_STORES;
import static cz.wincor.ovv.batch.BatchConstants.VALUE_DELIMITER;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import cz.wincor.ovv.batch.service.StoreService;
import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.entity.batch.ReportFile;
import cz.wincor.ovv.batch.entity.ovv.CountryCode;
import cz.wincor.ovv.batch.entity.ovv.OvvStore;
import cz.wincor.ovv.batch.support.OvvStoreManager;

/**
 * This Tasklet is responsible for searching for a related {@link ReportFile}
 * saved in DB
 *
 * <AUTHOR>
 *
 */
@Component("loadOvvStoresTasklet")
public class LoadOvvStoreTasklet implements Tasklet {

    private static final Logger LOG = LoggerFactory.getLogger(LoadOvvStoreTasklet.class);

    @Autowired
    private StoreService storeService;

    @Override
    public RepeatStatus execute(StepContribution paramStepContribution, ChunkContext paramChunkContext)
            throws Exception {
        String countryName = (String) paramChunkContext.getStepContext().getJobParameters().get(PARAM_OUTPUT_FILENAME_COUNTRY);
        String partnerIdParam = (String) paramChunkContext.getStepContext().getJobParameters().get(PARAM_PARTNER_ID);
        String storeFilterParam = (String) paramChunkContext.getStepContext().getJobParameters().get(PARAM_STORES);
        Validate.notEmpty(countryName, "Parameter " + PARAM_OUTPUT_FILENAME_COUNTRY + " must not be empty or null.");
        Validate.notEmpty(partnerIdParam, "Parameter " + PARAM_PARTNER_ID + " must not be empty or null.");
        CountryCode countryCode = CountryCode.fromValue(countryName);
        String[] partnerIds = partnerIdParam.split(VALUE_DELIMITER);

        List<String> storeFilter = Collections.emptyList();
        if (storeFilterParam != null) {
            storeFilter = Arrays.asList(storeFilterParam.split(VALUE_DELIMITER));
        }
        List<OvvStore> storeList = storeService.findByCountryCodeAndPartnerIdIn(countryCode, Arrays.asList(partnerIds));

        if (!storeFilter.isEmpty()) {
            LOG.info("Using store filter to filter found stores: {}", storeFilter);
            List<String> finalStoreFilter = storeFilter;
            storeList = storeList.stream().filter(p -> finalStoreFilter.contains(p.getSiteCode())).collect(Collectors.toList());
        }
        Validate.notEmpty(storeList, "For a defined country (" + countryCode
                + " and partner ids " + Arrays.asList(partnerIds) + ") are missing stores. " +
                "Please check if the STORE table contains stores for a defined country.");

        LOG.info("Successfully loaded {} stores to input country: {} and partnerIds: {}", storeList.size(), countryCode, partnerIdParam);

        paramChunkContext.getStepContext().getStepExecution().getJobExecution().getExecutionContext()
        .put(BatchConstants.PARAM_OVV_STORE_MANAGER, new OvvStoreManager(storeList));
        paramChunkContext.getStepContext().getStepExecution().getJobExecution().getExecutionContext()
                .put(BatchConstants.PARAM_PARTNER_ID, Arrays.asList(partnerIds));
        paramChunkContext.getStepContext().getStepExecution().getJobExecution().getExecutionContext()
                .put(BatchConstants.PARAM_STORES, storeFilter);
        return RepeatStatus.FINISHED;
    }
}
