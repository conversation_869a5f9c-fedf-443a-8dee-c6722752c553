package cz.wincor.ovv.batch.tasklet;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Set;

import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.UnexpectedJobExecutionException;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.InitializingBean;

/**
 * General tasklet is able to move required files into an archive folder
 *
 * <AUTHOR>
 *
 */
public class FileArchiveTasklet implements Tasklet, InitializingBean {

    private static final Logger LOG = LoggerFactory.getLogger(FileArchiveTasklet.class);

    private Set<String> filesToArchive;

    private String archiveDirectory;

    /**
     * This method archives required files into a archive folder
     *
     * @throws UnexpectedJobExecutionException
     *             if a moving file cannot be completed due to an error
     */
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) {
        Path archiveDirectoryPath = Paths.get(archiveDirectory);
        try {
            Files.createDirectories(archiveDirectoryPath);
        } catch (IOException e1) {
            throw new IllegalStateException(
                    "Reco local archive directory does not exit and cannot be created " + archiveDirectoryPath, e1);
        }
        if (archiveDirectoryPath.isAbsolute()) {
            Validate.isTrue(Files.isDirectory(archiveDirectoryPath));
        }
        for (String fileToArchive : filesToArchive) {
            Path sourcePath = Paths.get(fileToArchive);
            if (!Files.exists(sourcePath)) {
                LOG.warn("File {} cannot be archived, it does not exist.", sourcePath.toAbsolutePath());
                continue;
            }
            /**
             * Get source directory(getParent()), resolve to archive path(first
             * resolve()) and resolve to full path including file name (second
             * resolve())
             */
            Path targetPath = sourcePath.getParent().resolve(archiveDirectoryPath).resolve(sourcePath.getFileName());
            LOG.debug("Trying to move file from {} to {}", sourcePath.toAbsolutePath(), targetPath.toAbsolutePath());
            try {
                Files.move(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
            } catch (IOException e) {
                throw new UnexpectedJobExecutionException("Could not archive file " + sourcePath.toAbsolutePath()
                        + " to target " + targetPath.toAbsolutePath(), e);
            }
        }
        return RepeatStatus.FINISHED;
    }

    public void afterPropertiesSet() throws Exception {
        Validate.notNull(archiveDirectory, "Archive directory must be set.");
        Validate.notNull(filesToArchive, "List of files to archive must be set.");
    }

    public Set<String> getFilesToArchive() {
        return filesToArchive;
    }

    public void setFilesToArchive(Set<String> filesToArchive) {
        this.filesToArchive = filesToArchive;
    }

    public String getArchiveDirectory() {
        return archiveDirectory;
    }

    public void setArchiveDirectory(String archiveDirectory) {
        this.archiveDirectory = archiveDirectory;
    }
}
