package cz.wincor.ovv.batch.pojo;

/**
 * Response to voucher validation request (inside a batch)
 * <AUTHOR>
 *
 */
public class VoucherResponse extends Voucher {

    private Integer resultCode;
    private String resultDescription;
    public Integer getResultCode() {
        return resultCode;
    }
    public void setResultCode(Integer resultCode) {
        this.resultCode = resultCode;
    }
    public String getResultDescription() {
        return resultDescription;
    }
    public void setResultDescription(String resultDescription) {
        this.resultDescription = resultDescription;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("VoucherResponse{");
        sb.append("resultCode=").append(resultCode);
        sb.append(", resultDescription='").append(resultDescription).append('\'');
        sb.append(", voucherNumber='").append(getVoucherNumber()).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
