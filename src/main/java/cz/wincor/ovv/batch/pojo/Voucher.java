package cz.wincor.ovv.batch.pojo;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import cz.wincor.ovv.batch.entity.ovv.OvvStore;

/**
 * <PERSON><PERSON> class to wrap information about voucher validation to report
 *
 * <AUTHOR>
 *
 */
public class Voucher {

    private long id;
    private String voucherNumber;
    private String storeNumber;
    private String costCentre;
    private ZonedDateTime serverZonedDateTime;
    private ZonedDateTime posZonedDateTime;
    private static final String UNKNOWN = "unknown";
    private String timezone;
    private OvvStore ovvStore;
    private String partnerId;
    private String deviceId;
    private int stan;
    private TransactionType transactionType;

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public int getStan() {
        return stan;
    }

    public void setStan(int stan) {
        this.stan = stan;
    }

    public TransactionType getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(TransactionType transactionType) {
        this.transactionType = transactionType;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getVoucherNumber() {
        return voucherNumber;
    }

    public void setVoucherNumber(String voucherNumber) {
        this.voucherNumber = voucherNumber;
    }

    public String getStoreNumber() {
        return storeNumber;
    }

    public void setStoreNumber(String storeNumber) {
        this.storeNumber = storeNumber;
    }

    public final String getCostCentre() {
        return costCentre;
    }

    public final void setCostCentre(String costCentre) {
        this.costCentre = costCentre;
    }

    public String getPosDateTimeIsoFormat() {
        if (posZonedDateTime != null) {
            return DateTimeFormatter.ISO_OFFSET_DATE_TIME
                    .format(posZonedDateTime.withZoneSameInstant(ZoneId.of(timezone)));
        } else {
            return UNKNOWN;
        }
    }

    public String getServerDateTimeIsoFormat() {
        if (serverZonedDateTime != null) {
            return DateTimeFormatter.ISO_OFFSET_DATE_TIME
                    .format(serverZonedDateTime.withZoneSameInstant(ZoneId.of(timezone)));
        } else {
            return UNKNOWN;
        }
    }

    public final String getTimezone() {
        return timezone;
    }

    public final void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public final ZonedDateTime getServerZonedDateTime() {
        return serverZonedDateTime;
    }

    public final void setServerZonedDateTime(ZonedDateTime serverZonedDateTime) {
        this.serverZonedDateTime = serverZonedDateTime;
    }

    public final ZonedDateTime getPosZonedDateTime() {
        return posZonedDateTime;
    }

    public final void setPosZonedDateTime(ZonedDateTime posZonedDateTime) {
        this.posZonedDateTime = posZonedDateTime;
    }

    public OvvStore getOvvStore() {
        return ovvStore;
    }

    public void setOvvStore(OvvStore store) {
        this.ovvStore = store;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

}
