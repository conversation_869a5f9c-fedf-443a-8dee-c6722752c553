package cz.wincor.ovv.batch.pojo;

/**
 * Result codes used in issuer's response files
 * <AUTHOR>
 *
 */
public enum ResultCode {

    CONFIRMED(0), NOT_CONFIRMED_INT(1), NOT_CONFIRMED_EXT(2), ALREADY_USED(3), FAKE(4);

    private int code;

    private ResultCode(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    /**
     * Find {@link ResultCode} to an input code, if not found {@link IllegalArgumentException} is thrown
     * @param code numeric response code
     * @return {@link ResultCode} or {@link IllegalArgumentException} if not found
     */
    public static ResultCode getResultCodeByCode(int code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode() == code) {
                return resultCode;
            }
        }
        throw new IllegalArgumentException("Result code " + code + " does not exist.");
    }

}
