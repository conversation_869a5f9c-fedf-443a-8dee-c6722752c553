package cz.wincor.ovv.batch.service;

import cz.wincor.ovv.batch.entity.batch.ReportFile;
import cz.wincor.ovv.batch.repository.batch.ReportFileRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Transactional("batchTransactionManager")
public class ReportFileService {

    @Autowired
    private ReportFileRepository reportFileRepository;

    public ReportFile save(ReportFile reportFile) {
        return reportFileRepository.save(reportFile);
    }

    public ReportFile getById(long id) {
        return reportFileRepository.findById(id).orElse(null);
    }

    public ReportFile findByUuidAndOvvIssuerIgnoreCase(long uuid, String ovvIssuer) {
        return reportFileRepository.findByUuidAndOvvIssuerIgnoreCase(uuid, ovvIssuer);
    }

    public ReportFile findByJobExecutionId(long jobExecutionId) {
        return reportFileRepository.findByJobExecutionId(jobExecutionId);
    }

    public void deleteAll() {
        reportFileRepository.deleteAll();
    }
}
