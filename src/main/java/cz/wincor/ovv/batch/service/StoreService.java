package cz.wincor.ovv.batch.service;

import cz.wincor.ovv.batch.entity.ovv.CountryCode;
import cz.wincor.ovv.batch.entity.ovv.OvvStore;
import cz.wincor.ovv.batch.repository.ovv.StoreRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Transactional("ovvTransactionManager")
public class StoreService {

    @Autowired
    private StoreRepository storeRepository;

    public List<OvvStore> findByCountryCodeAndPartnerIdIn(CountryCode countryCode, List<String> partnerIds) {
        return storeRepository.findByCountryCodeAndPartnerIdIn(countryCode, partnerIds);
    }

}
