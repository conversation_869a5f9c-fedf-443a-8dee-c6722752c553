package cz.wincor.ovv.batch.service;

import cz.wincor.ovv.batch.entity.batch.ReportBatch;
import cz.wincor.ovv.batch.entity.batch.ReportFile;
import cz.wincor.ovv.batch.repository.batch.ReportBatchRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Transactional("batchTransactionManager")
public class ReportBatchService {

    @Autowired
    private ReportBatchRepository reportBatchRepository;

    public ReportBatch save(ReportBatch reportBatch) {
        return reportBatchRepository.save(reportBatch);
    }

    public List<ReportBatch> saveAll(List<ReportBatch> reportBatches) {
        return reportBatchRepository.saveAll(reportBatches);
    }

    public ReportBatch getById(Long id) {
        return reportBatchRepository.findById(id).orElse(null);
    }

    public int setResultToReportBatch(long reportFileId, int resultCode, String resultDescription, String voucherNumber) {
        return reportBatchRepository.setResultToReportBatch(reportFileId, resultCode, resultDescription, voucherNumber);
    }

    public List<ReportBatch> findByReportFile(ReportFile reportFile) {
        return reportBatchRepository.findByReportFile(reportFile);
    }

    public void deleteAll() {
        reportBatchRepository.deleteAll();
    }
}
