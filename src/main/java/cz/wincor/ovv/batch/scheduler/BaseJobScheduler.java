package cz.wincor.ovv.batch.scheduler;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameter;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersInvalidException;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobInstanceAlreadyCompleteException;
import org.springframework.batch.core.repository.JobRestartException;

import cz.wincor.ovv.batch.BatchConstants;

/**
 * Base job scheduler responsible for running a batch job
 * <AUTHOR>
 *
 */
public abstract class BaseJobScheduler implements Runnable {

    private JobLauncher launcher;

    private Job job;

    private JobParameters jobParameters;

    private boolean enabled;

    private static final Logger LOG = LoggerFactory.getLogger(BaseJobScheduler.class);

    public void run() {
        if (!enabled) {
            LOG.info("Scheduler for job {} is disabled.", job.getName());
            return;
        }
        try {
            if (jobParameters == null) {
                jobParameters = new JobParameters();
            }
            Map<String, JobParameter<?>> map = new HashMap<>(jobParameters.getParameters());
            if (!map.containsKey(BatchConstants.PARAM_REPORT_START_DATE)) {
                setParameters(map);
            }
            JobExecution execution = launcher.run(job, new JobParameters(map));
            LOG.info("Execution status: " + execution.getStatus());
        } catch (JobExecutionAlreadyRunningException e) {
            LOG.error("Job is already running.", e);
        } catch (JobRestartException e) {
            LOG.error("Illegal attempt to restart a job.", e);
        } catch (JobInstanceAlreadyCompleteException e) {
            LOG.error("Illegal attempt to restart a job that was already completed successfully.", e);
        } catch (JobParametersInvalidException e) {
            LOG.error("Some JobParameters are invalid.", e);
        }
    }

    /**
     * @param map
     */
    protected abstract void setParameters(Map<String, JobParameter<?>> map);

    public void setLauncher(JobLauncher launcher) {
        this.launcher = launcher;
    }

    public void setJob(Job job) {
        this.job = job;
    }

    public void setJobParameters(JobParameters jobParameters) {
        this.jobParameters = jobParameters;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
