package cz.wincor.ovv.batch.scheduler;

import java.time.Clock;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;

import org.springframework.batch.core.JobParameter;

import cz.wincor.ovv.batch.BatchConstants;
import cz.wincor.ovv.batch.pojo.ReportPeriod;

/**
 * Weekly job scheduler responsible for running a batch for a last week
 * <AUTHOR>
 *
 */
public class WeeklyJobScheduler extends BaseJobScheduler {


    private Clock clock = Clock.systemDefaultZone();

    /**
     * Update input map with {@link WeeklyJobScheduler} related parameters
     * @param map
     */
    protected void setParameters(Map<String, JobParameter<?>> map) {
        //Set Monday in the last week
        map.put(BatchConstants.PARAM_REPORT_START_DATE, new JobParameter<>(
                LocalDate.now(clock).minusWeeks(1).with(DayOfWeek.MONDAY).format(DateTimeFormatter.BASIC_ISO_DATE), String.class));
        map.put(BatchConstants.PARAM_REPORT_PERIOD, new JobParameter<>(ReportPeriod.WEEK.name(), String.class));
    }

    public Clock getClock() {
        return clock;
    }

    public void setClock(Clock clock) {
        this.clock = clock;
    }


}
