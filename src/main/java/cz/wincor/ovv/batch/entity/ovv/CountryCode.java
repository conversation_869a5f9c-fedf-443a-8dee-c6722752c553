package cz.wincor.ovv.batch.entity.ovv;

/**
 * Enum for available country codes
 *
 * <AUTHOR>
 *
 */
public enum CountryCode {

    CZ(1), PL(3), HU(4), SK(2);

    private int prefix;

    private CountryCode(int prefix) {
        this.prefix = prefix;
    }

    public String value() {
        return name();
    }

    public static CountryCode fromValue(String v) {
        return valueOf(v);
    }

    public int getPrefix() {
        return prefix;
    }

}
