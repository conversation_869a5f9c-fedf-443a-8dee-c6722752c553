package cz.wincor.ovv.batch.entity.ovv;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

@Entity
@Table(name = "OVV_STORE", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"country_code", "partner_id", "site_code"}) })
public class OvvStore {

    @Id
    @SequenceGenerator(name = "ovvStoreSequenceGenerator", sequenceName = "ovv_store_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ovvStoreSequenceGenerator")
    private Long id;

    @Column(name = "country_code", length = 2, nullable = false)
    @Enumerated(EnumType.STRING)
    private CountryCode countryCode;

    @Column(name = "partner_id")
    private String partnerId;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "site_code", nullable = false)
    private String siteCode;

    @Column(name = "cost_centre", nullable = false)
    private String costCentre;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public CountryCode getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(CountryCode countryCode) {
        this.countryCode = countryCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getCostCentre() {
        return costCentre;
    }

    public void setCostCentre(String costCentre) {
        this.costCentre = costCentre;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("OvvStore(").append(id).append(")");
        return builder.toString();
    }



}
