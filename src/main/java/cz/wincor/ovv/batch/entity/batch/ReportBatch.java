package cz.wincor.ovv.batch.entity.batch;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

/**
 * Report Batch keeps information about relationship between vouchers and batch
 * files, what are related to.
 *
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "REPORT_BATCH", indexes = { @Index(columnList = "report_file_id"),
        @Index(columnList = "voucher_number") }, uniqueConstraints = {
                @UniqueConstraint(name = "IDX_VOUCHER_TO_REPORT_FILE", columnNames = { "voucher_number",
                        "report_file_id" }) })
public class ReportBatch {

    @Id
    @SequenceGenerator(name = "reportBatchSequenceGenerator", sequenceName = "report_batch_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "reportBatchSequenceGenerator")
    private Long id;

    @Column(name = "voucher_number")
    private String voucherNumber;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "report_file_id", nullable = false)
    private ReportFile reportFile;

    @Column(name = "ovv_transaction_request_id")
    private Long ovvTransactionRequestId;

    @Column(name = "result_code")
    private Integer resultCode;

    @Column(name = "result_description")
    private String resultDescription;

    @Column(name = "store_id", nullable = false)
    private Long ovvStoreId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVoucherNumber() {
        return voucherNumber;
    }

    public void setVoucherNumber(String voucherNumber) {
        this.voucherNumber = voucherNumber;
    }

    public ReportFile getReportFile() {
        return reportFile;
    }

    public void setReportFile(ReportFile reportFile) {
        this.reportFile = reportFile;
    }

    public Long getOvvTransactionRequestId() {
        return ovvTransactionRequestId;
    }

    public void setOvvTransactionRequestId(Long ovvTransactionRequestId) {
        this.ovvTransactionRequestId = ovvTransactionRequestId;
    }

    public String getResultDescription() {
        return resultDescription;
    }

    public void setResultDescription(String resultDescription) {
        this.resultDescription = resultDescription;
    }

    public Integer getResultCode() {
        return resultCode;
    }

    public void setResultCode(Integer resultCode) {
        this.resultCode = resultCode;
    }

    public Long getOvvStoreId() {
        return ovvStoreId;
    }

    public void setOvvStoreId(Long ovvStoreId) {
        this.ovvStoreId = ovvStoreId;
    }

}
