package cz.wincor.ovv.batch.entity.batch;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import cz.wincor.ovv.batch.entity.ovv.CountryCode;

/**
 * Report file entity is keeps information about generated files during batch
 * processing
 *
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "REPORT_FILE", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"ovv_issuer", "uuid"}) })
public class ReportFile {

    @Id
    @SequenceGenerator(name = "reportFileSequenceGenerator", sequenceName = "report_file_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "reportFileSequenceGenerator")
    private Long id;

    @Column(name = "file_name", nullable = false)
    private String fileName;

    @Column(name = "file_path", nullable = false)
    private String filePath;

    @Column(name = "ovv_issuer", nullable = false)
    private String ovvIssuer;

    @Column(name = "filename_date", nullable = false)
    private String fileNameDate;

    @Column(name = "uuid", nullable = false)
    private Long uuid;

    @Column(name = "items_count", nullable = false)
    private Integer itemsCount;

    @Column(name = "job_execution_id", nullable = false)
    private Long jobExecutionId;

    @Column(name = "reco_job_execution_id")
    private Long recoJobExecutionId;

    @Column(name = "created", nullable = false)
    private Date created;

    @Column(name = "reconciliated")
    private Date reconciliated;

    @Column(name = "country_code", length = 2, nullable = false)
    @Enumerated(EnumType.STRING)
    private CountryCode countryCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getOvvIssuer() {
        return ovvIssuer;
    }

    public void setOvvIssuer(String ovvIssuer) {
        this.ovvIssuer = ovvIssuer;
    }

    public String getFileNameDate() {
        return fileNameDate;
    }

    public void setFileNameDate(String fileNameDate) {
        this.fileNameDate = fileNameDate;
    }

    public Long getUuid() {
        return uuid;
    }

    public void setUuid(Long uuid) {
        this.uuid = uuid;
    }

    public Long getJobExecutionId() {
        return jobExecutionId;
    }

    public void setJobExecutionId(Long jobExecutionId) {
        this.jobExecutionId = jobExecutionId;
    }

    public Long getRecoJobExecutionId() {
        return recoJobExecutionId;
    }

    public void setRecoJobExecutionId(Long recoJobExecutionId) {
        this.recoJobExecutionId = recoJobExecutionId;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getReconciliated() {
        return reconciliated;
    }

    public void setReconciliated(Date reconciliated) {
        this.reconciliated = reconciliated;
    }

    public Integer getItemsCount() {
        return itemsCount;
    }

    public void setItemsCount(Integer itemsCount) {
        this.itemsCount = itemsCount;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("ReportFile [id=").append(id).append(", fileName=").append(fileName).append(", filePath=")
                .append(filePath).append(", ovvIssuer=").append(ovvIssuer).append(", fileNameDate=")
                .append(fileNameDate).append(", uuid=").append(uuid).append(", itemsCount=").append(itemsCount)
                .append(", jobExecutionId=").append(jobExecutionId).append(", recoJobExecutionId=")
                .append(recoJobExecutionId).append(", created=").append(created).append(", reconciliated=")
                .append(reconciliated).append("]");
        return builder.toString();
    }

    public CountryCode getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(CountryCode countryCode) {
        this.countryCode = countryCode;
    }

}
