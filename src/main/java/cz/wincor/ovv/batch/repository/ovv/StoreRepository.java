package cz.wincor.ovv.batch.repository.ovv;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import cz.wincor.ovv.batch.entity.ovv.CountryCode;
import cz.wincor.ovv.batch.entity.ovv.OvvStore;

/**
 * Repository for {@link OvvStore} entity.
 *
 * <AUTHOR>
 */
@Repository
public interface StoreRepository extends JpaRepository<OvvStore, Long> {

    public List<OvvStore> findByCountryCodeAndPartnerIdIn(CountryCode countryCode, List<String> partnerIds);
}
