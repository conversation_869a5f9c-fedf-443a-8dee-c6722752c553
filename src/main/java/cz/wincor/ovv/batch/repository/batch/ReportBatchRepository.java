package cz.wincor.ovv.batch.repository.batch;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import cz.wincor.ovv.batch.entity.batch.ReportBatch;
import cz.wincor.ovv.batch.entity.batch.ReportFile;

/**
 * Repository for {@link ReportBatch} entity.
 *
 * <AUTHOR>
 */
@Repository
public interface ReportBatchRepository
        extends JpaRepository<ReportBatch, Long> {

    /**
     * Find list of {@link ReportBatch} by its {@link ReportFile}
     *
     * @param reportFile
     * @return
     */
    public List<ReportBatch> findByReportFile(ReportFile reportFile);

    /**
     * Update {@link ReportBatch} with a reconciliation result
     *
     * @param resultCode
     * @param resultDescription
     * @param voucherNumber
     * @return
     */
    @Modifying
    @Query("update ReportBatch b set b.resultCode = :resultCode, b.resultDescription = :resultDescription where b.reportFile.id = :reportFileId and b.voucherNumber = :voucherNumber")
    public int setResultToReportBatch(@Param("reportFileId") long reportFileId, @Param("resultCode") int resultCode,
            @Param("resultDescription") String resultDescription, @Param("voucherNumber") String voucherNumber);

}
