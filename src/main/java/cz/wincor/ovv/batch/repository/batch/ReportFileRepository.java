package cz.wincor.ovv.batch.repository.batch;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import cz.wincor.ovv.batch.entity.batch.ReportFile;

/**
 * Repository for {@link ReportFile} entity.
 *
 * <AUTHOR>
 */
@Repository
public interface ReportFileRepository extends JpaRepository<ReportFile, Long> {

    /**
     * Find {@link ReportFile} by its jobExecutionId
     *
     * @param jobExecutionId
     * @return
     */
    public ReportFile findByJobExecutionId(long jobExecutionId);

    public ReportFile findByUuidAndOvvIssuerIgnoreCase(long uuid, String ovvIssuer);

}
