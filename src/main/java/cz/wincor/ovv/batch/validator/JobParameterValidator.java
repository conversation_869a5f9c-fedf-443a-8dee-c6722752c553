package cz.wincor.ovv.batch.validator;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.lang3.Validate;
import org.springframework.batch.core.JobExecutionException;
import org.springframework.batch.core.JobParameter;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersInvalidException;
import org.springframework.batch.core.JobParametersValidator;
import org.springframework.beans.factory.InitializingBean;

/**
 * Validate required parameters before run job
 *
 * <AUTHOR>
 *
 */
public class JobParameterValidator implements JobParametersValidator, InitializingBean {

    private Set<String> requiredParameters = new HashSet<>();

    /*
     * (non-Javadoc)
     *
     * @see org.springframework.batch.core.JobParametersValidator#validate(org.
     * springframework.batch.core.JobParameters)
     */
    @Override
    public void validate(JobParameters parameters) throws JobParametersInvalidException {
        try {
            validateParametersIfHaveText(parameters, requiredParameters.toArray(new String[] {}));
        } catch (IllegalArgumentException | NullPointerException e) {
            throw new JobParametersInvalidException("Validation error before run a job: " + e.getMessage());
        }

    }

    /**
     * Validate all parameters if have text (i.e. not null, not empty and not
     * blank)
     *
     * @param parameters
     * @param keys
     */
    private void validateParametersIfHaveText(JobParameters parameters, String... keys) {
        for (String key : keys) {
            JobParameter<?> param = parameters.getParameter(key);
            Objects.requireNonNull(param, "JobParameter " + key + " is required and must not be null");
            if (param.getType().equals(String.class)) {
                Validate.notEmpty((String) param.getValue(), "JobParameter " + key + " is required and must not be empty.");
            }
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (requiredParameters == null || requiredParameters.isEmpty()) {
            throw new JobExecutionException("There is no parameters to validate.");
        }
    }

    public Set<String> getRequiredParameters() {
        return requiredParameters;
    }

    public void setRequiredParameters(Set<String> requiredParameters) {
        this.requiredParameters = requiredParameters;
    }

}
