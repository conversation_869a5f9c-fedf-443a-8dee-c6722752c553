package cz.wincor.ovv.batch;

/**
 * This class groups constants related to the OVV Batch application
 *
 * <AUTHOR>
 *
 */
public class BatchConstants {
    // General constants definitions
    public static final String CHARSET_UTF8 = "UTF-8";
    public static final String TRUE = "TRUE";
    public static final String VALUE_DELIMITER = ",";


    // Parameters definitions
    public static final String PARAM_FAIL_ON_MISSING = "failOnMissingVoucher";
    public static final String PARAM_REPORT_START_DATE = "reportStartDate";
    public static final String PARAM_REPORT_PERIOD = "reportPeriod";
    public static final String PARAM_CSV_SUFFIX = "csv";
    public static final String PARAM_ERR_SUFFIX = "err";
    public static final String PARAM_OUTPUT_FILENAME_COUNTRY = "output.file.name.country";
    public static final String PARAM_OVV_ISSUER = "ovvIssuer";
    public static final String PARAM_PARTNER_ID = "partnerId";
    public static final String PARAM_STORES = "stores";
    public static final String PARAM_OUTPUT_PATH = "output.file.path";
    public static final String PARAM_READER_START = "reader.startDateTime";
    public static final String PARAM_READER_END = "reader.endDateTime";
    public static final String PARAM_RECEIVER_TIMEZONE = "receiverTimezone";
    public static final String PARAM_INPUT_FILENAME = "input.file.name";
    public static final String PARAM_FILE_OUTPUT_DELIMITER = "output.file.delimiter";
    public static final String PARAM_FILE_OUTPUT_COLUMNS = "output.file.columns";
    public static final String PARAM_PAGE_SIZE = "pageSize";
    public static final String PARAM_RECO_ERROR_FILE = "recoErrorFile";
    public static final String PARAM_REPORT_FILE = "reportFile";
    public static final String PARAM_OVV_STORE_MANAGER = "ovvStoreManager";
    public static final String PARAM_FILE_TO_UPLOAD = "fileToUpload";
    public static final String PARAM_NUMBER_OF_ITEMS_IN_BATCH = "batchItemsCount";

}
