package cz.wincor.ovv.batch.support;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.ZoneId;

import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.jdbc.core.RowMapper;

import cz.wincor.ovv.batch.entity.ovv.CountryCode;
import cz.wincor.ovv.batch.entity.ovv.OvvStore;
import cz.wincor.ovv.batch.pojo.TransactionType;
import cz.wincor.ovv.batch.pojo.Voucher;
import cz.wincor.ovv.batch.util.DbUtil;

/**
 * Mapper to map incoming DB result set into {@link Voucher}
 *
 * <AUTHOR>
 *
 */
public class VoucherRowMapper implements RowMapper<Voucher>, InitializingBean {

    private OvvStoreManager storeManager;

    private CountryCode countryCode;

    private String receiverTimezone;

    private static final Logger logger = LoggerFactory.getLogger(VoucherRowMapper.class);

    @Override
    public Voucher mapRow(ResultSet rs, int rowNum) throws SQLException {
        Voucher voucher = new Voucher();
        voucher.setTimezone(receiverTimezone);
        voucher.setId(rs.getLong("requestId"));
        voucher.setVoucherNumber(rs.getString("voucherNumber"));
        voucher.setPartnerId(rs.getString("partnerId"));
        String paymentPlace = rs.getString("paymentPlace");
        voucher.setStoreNumber(paymentPlace);
        try {
            if (storeManager != null) {
                OvvStore store = storeManager.getOvvStore(voucher.getStoreNumber());
                if (store == null) {
                    logger.warn("No store found for partner id {} and store number {}", voucher.getStoreNumber());
                } else {
                    voucher.setCostCentre(store.getCostCentre());
                    voucher.setOvvStore(store);
                }
            } else {
                logger.warn(
                        "Store manager is missing, so no OVV store will not be found for partner id {} and store number {}",
                        voucher.getPartnerId(), voucher.getStoreNumber());
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException(
                    "Payment place should contain a store number, but non-numeric value present: "
                            + rs.getString("paymentPlace"),
                    e);
        }

        // All local datetimes from DB are expected to be in the same zone as
        // system default
        Timestamp deviceLocalDateTime = rs.getTimestamp("deviceLocalDateTime");
        if (deviceLocalDateTime != null) {
            voucher.setPosZonedDateTime(
                    rs.getTimestamp("deviceLocalDateTime").toLocalDateTime().atZone(ZoneId.systemDefault()));
        }

        Timestamp serverLocalDateTime = rs.getTimestamp("serverLocalDateTime");
        if (serverLocalDateTime != null) {
            voucher.setServerZonedDateTime(
                    rs.getTimestamp("serverLocalDateTime").toLocalDateTime().atZone(ZoneId.systemDefault()));
        }

        if (DbUtil.hasColumn(rs, "stan")) {
            voucher.setStan(rs.getInt("stan"));
        }

        if (DbUtil.hasColumn(rs, "deviceId")) {
            voucher.setDeviceId(rs.getString("deviceId"));
        }

        if (DbUtil.hasColumn(rs, "transactionType")) {
            String transactionType = rs.getString("transactionType");
            switch (transactionType) {
                case "VALIDATION":
                    voucher.setTransactionType(TransactionType.A);
                    break;
                case "REVERSAL":
                    voucher.setTransactionType(TransactionType.R);
                    break;
                default:
                    throw new IllegalArgumentException("Unsupported transaction type: " + transactionType);
            }
        }


        return voucher;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Validate.notNull(countryCode, "Country code is required.");
        Validate.notNull(receiverTimezone, "ReceiverTimezone is required.");
    }

    public final void setCountryCode(CountryCode countryCode) {
        this.countryCode = countryCode;
    }

    public final void setReceiverTimezone(String receiverTimezone) {
        this.receiverTimezone = receiverTimezone;
    }

    public void setStoreManager(OvvStoreManager storeManager) {
        this.storeManager = storeManager;
    }
}
