package cz.wincor.ovv.batch.support;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import cz.wincor.ovv.batch.entity.ovv.OvvStore;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OvvStoreManager {

    private transient Map<String, OvvStore> stores;

    public OvvStoreManager() {
        // used by Jackson
        stores = new HashMap<>();
    }

    public OvvStoreManager(List<OvvStore> storeList) {
        stores = storeList.stream().collect(Collectors.toMap(p -> p.getSiteCode(), p -> p));
    }

    public OvvStore getOvvStore(String siteCode) {
        return stores.get(siteCode);
    }

}
