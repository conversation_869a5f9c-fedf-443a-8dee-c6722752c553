package cz.wincor.ovv.batch.support;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

import org.apache.commons.io.FilenameUtils;
import org.springframework.integration.file.filters.AbstractFileListFilter;

/**
 * File List filter is able to define max repeat count to be able to accept
 * incoming file up to defined limit
 * <p>
 * This filter is useful when repeatedly downloading remote file (e.g. from SFTP
 * server), but fails with error, so you can set max repeat count to be applied
 * to this process
 *
 * <AUTHOR>
 *
 * @param <F>
 */
public class RepeatedFileListFilter extends AbstractFileListFilter<File> {

    private final Map<File, Integer> seenSet = new HashMap<>();

    private final Object monitor = new Object();

    private String patternToMatch;

    // Default value 1 is equal to accept only once
    private int maxRepeatCount = 1;

    /**
     * Default Constructor with maxRepeatCount equal to 1 (= accept each of an
     * incoming request file only once)
     */
    public RepeatedFileListFilter() {
    }

    /**
     * Constructor is possible to define custom maxRepeatCount
     *
     * @param maxRepeatCount
     */
    public RepeatedFileListFilter(int maxRepeatCount) {
        this.maxRepeatCount = maxRepeatCount;
    }

    /**
     * Constructor is possible to define custom maxRepeatCount and pattern to match the file name (excluding path, only file name with extensions)
     *
     * @param maxRepeatCount
     */
    public RepeatedFileListFilter(int maxRepeatCount, String patternToMatch) {
        this(maxRepeatCount);
        this.patternToMatch = patternToMatch;
    }

    @Override
    public boolean accept(File file) {
        //If pattern is not null, so validation against this pattern should be made
        if (patternToMatch != null) {
            //If the pattern is not matched, do not accept this file
            if (!Pattern.matches(patternToMatch, FilenameUtils.getName(file.getAbsolutePath()))) {
                return false;
            }
        }
        synchronized (this.monitor) {
            if (!this.seenSet.containsKey(file)) {
                this.seenSet.put(file, 1);
                return true;
            } else {
                int requests = this.seenSet.get(file) + 1;
                if (requests > maxRepeatCount) {
                    return false;
                } else {
                    this.seenSet.put(file, this.seenSet.get(file) + 1);
                    return true;
                }
            }
        }
    }
}
