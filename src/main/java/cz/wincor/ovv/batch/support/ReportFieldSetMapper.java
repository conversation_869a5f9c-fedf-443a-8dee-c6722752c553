package cz.wincor.ovv.batch.support;

import org.springframework.batch.item.file.mapping.FieldSetMapper;
import org.springframework.batch.item.file.transform.FieldSet;
import org.springframework.validation.BindException;

import cz.wincor.ovv.batch.pojo.VoucherResponse;

/**
 * Map incoming CSV line (already mapped to FieldSet) into a {@link VoucherResponse} object
 * <AUTHOR>
 *
 */
public class ReportFieldSetMapper implements FieldSetMapper<VoucherResponse> {

    @Override
    public VoucherResponse mapFieldSet(FieldSet fieldSet) throws BindException {
        VoucherResponse voucherResponse = new VoucherResponse();
        voucherResponse.setVoucherNumber(fieldSet.readString(0));
        voucherResponse.setResultCode(fieldSet.readInt(1));
        if (fieldSet.getFieldCount() > 2) {
            voucherResponse.setResultDescription(fieldSet.readString(2));
        }
        return voucherResponse;
    }

}
