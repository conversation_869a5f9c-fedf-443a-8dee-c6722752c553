package cz.wincor.ovv.batch.support;

import org.apache.commons.lang3.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.GenericMessage;

/**
 * Control bus to start/stop an adapter if required
 * <AUTHOR>
 *
 */
public class AdapterControlBusStarter implements InitializingBean, ApplicationListener<ContextRefreshedEvent> {

    private static final Logger LOG = LoggerFactory.getLogger(AdapterControlBusStarter.class);

    private boolean shouldStartImmediately;

    private String adapterName;

    private MessageChannel controlBusChannel;


    /**
     * @param shouldStartImmediately whether an adapter should be started immediately after a constructor call
     * @param adapterName name of the adapter
     * @param controlBusChannel {@link MessageChannel} where to send a "start" message
     */
    public AdapterControlBusStarter(boolean shouldStartImmediately, String adapterName, MessageChannel controlBusChannel) {
        super();
        this.shouldStartImmediately = shouldStartImmediately;
        this.adapterName = adapterName;
        this.controlBusChannel = controlBusChannel;
    }



    @Override
    public void afterPropertiesSet() throws Exception {
        Validate.notNull(controlBusChannel, "Control bus channel must be set.");
        Validate.notEmpty(adapterName, "Adapter name must not be null or empty.");
    }

    public void start() {
        controlBusChannel.send(new GenericMessage<String>("@" + adapterName + ".start()"));
        LOG.info("Starting adapter {}...", adapterName);
    }

    public void stop() {
        controlBusChannel.send(new GenericMessage<String>("@" + adapterName + ".stop()"));
        LOG.info("Stopping adapter {}...", adapterName);
    }



    @Override
    public void onApplicationEvent(ContextRefreshedEvent context) {
        if (shouldStartImmediately) {
            start();
        }

    }

}
