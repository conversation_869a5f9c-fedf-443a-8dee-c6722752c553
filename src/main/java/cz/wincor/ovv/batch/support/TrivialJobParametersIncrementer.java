package cz.wincor.ovv.batch.support;

import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.JobParametersIncrementer;

/**
 * Simple parameter incrementer to run job with same parameters repeatedly if
 * required
 *
 * <AUTHOR>
 *
 */
public class TrivialJobParametersIncrementer implements JobParametersIncrementer {

    public JobParameters getNext(JobParameters parameters) {
        if (parameters == null || parameters.isEmpty()) {
            return new JobParametersBuilder().addLong("run.id", 1L).toJobParameters();
        }
        String runIdString = parameters.getString("run.id", "0");

        long id = Long.valueOf(runIdString) + 1;
        return new JobParametersBuilder(parameters).addLong("run.id", id).toJobParameters();
    }

}
