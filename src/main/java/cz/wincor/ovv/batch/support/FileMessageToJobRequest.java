package cz.wincor.ovv.batch.support;

import java.io.File;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.integration.launch.JobLaunchRequest;
import org.springframework.messaging.Message;

import cz.wincor.ovv.batch.BatchConstants;

/**
 * Convert incoming message containing file into a {@link JobLaunchRequest}
 * <AUTHOR>
 *
 */
public class FileMessageToJobRequest {
    private Job job;
    private JobParameters jobParameters;

    public void setJob(Job job) {
        this.job = job;
    }

    /**
     * Convert {@link Message} containing {@link File} into a {@link JobLaunchRequest}
     * @param message
     * @return
     */
    public JobLaunchRequest toRequest(Message<File> message) {
        if (jobParameters == null) {
            jobParameters = new JobParameters();
        }
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder(jobParameters);
        jobParametersBuilder.addString(BatchConstants.PARAM_INPUT_FILENAME, message.getPayload().getAbsolutePath());

        return new JobLaunchRequest(job, jobParametersBuilder.toJobParameters());
    }

    public void setJobParameters(JobParameters jobParameters) {
        this.jobParameters = jobParameters;
    }
}
