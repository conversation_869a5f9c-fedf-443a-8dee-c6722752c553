package cz.wincor.ovv.batch.util;

import org.apache.commons.lang3.Validate;

/**
 * Filename helper to work with batch filenames easily
 * <AUTHOR>
 *
 */
public class FilenameHelper {

    private static final String DEFAULT_PREFIX = "WN";
    private static final String FIELD_DELIMITER = "_";
    private static final String SUFFIX_DELIMITER = ".";
    private static final String SUFFIX_DELIMITER_REGEX = "\\.";
    private String prefix;
    private String country;
    private String issuer;
    private String reportStartDate;
    private long uuid;
    private String suffix1;
    private String suffix2;

    /**
     * Parse input filename and return an instance of {@link FilenameHelper}, example of input:
     * WN_CZ_MEALVO_20160414_1460713440000.csv or WN_CZ_MEALVO_20160414_1460713440000.csv.err
     * @param input filename to be parsed
     * @return instance of {@link FilenameHelper} or {@link IllegalArgumentException} or {@link NumberFormatException} if any error during parsing
     */
    public static FilenameHelper parseFromString(String input) {
        FilenameHelper filenameHelper = new FilenameHelper();
        Validate.notEmpty(input, "Input string is empty, no filename cannot be parsed.");
        // Filename may have multiple suffix but minimal 1 and maximum 2, e.g.
        // example.csv and example.csv.err
        String[] mainParts = input.split(SUFFIX_DELIMITER_REGEX);
        if (mainParts.length > 3 || mainParts.length < 2) {
            throw new IllegalArgumentException("Unsupported filename format, expected 2 or 3 dots only.");
        }
        filenameHelper.setSuffix1(mainParts[1]);
        if (mainParts.length == 3) {
            filenameHelper.setSuffix2(mainParts[2]);
        }
        String[] filenameValues = mainParts[0].split(FIELD_DELIMITER);
        if (filenameValues.length != 5) {
            throw new IllegalArgumentException(
                    "Unsupported filename format, expected 5 fields delimited by underscore (without suffixes and extensions)");
        }
        filenameHelper.setPrefix(filenameValues[0]);
        filenameHelper.setCountry(filenameValues[1]);
        filenameHelper.setIssuer(filenameValues[2]);
        filenameHelper.setReportDate(filenameValues[3]);
        filenameHelper.setUuid(Long.valueOf(filenameValues[4]));
        return filenameHelper;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public String getReportDate() {
        return reportStartDate;
    }

    public void setReportDate(String reportStartDate) {
        this.reportStartDate = reportStartDate;
    }

    public long getUuid() {
        return uuid;
    }

    public void setUuid(long uuid) {
        this.uuid = uuid;
    }

    public String getSuffix1() {
        return suffix1;
    }

    public void setSuffix1(String suffix1) {
        this.suffix1 = suffix1;
    }

    public String getSuffix2() {
        return suffix2;
    }

    public void setSuffix2(String suffix2) {
        this.suffix2 = suffix2;
    }

    /**
     * Convert {@link FilenameHelper} to a real filename, e.g.WN_CZ_MEALVO_20160414_1460713440000.csv or WN_CZ_MEALVO_20160414_1460713440000.csv.err
     */
    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        if (prefix == null) {
            builder.append(DEFAULT_PREFIX);
        } else {
            builder.append(prefix);
        }
        builder.append(FIELD_DELIMITER)
                .append(country)
                .append(FIELD_DELIMITER)
                .append(issuer)
                .append(FIELD_DELIMITER)
                .append(reportStartDate)
                .append(FIELD_DELIMITER)
                .append(uuid)
                .append(SUFFIX_DELIMITER)
                .append(suffix1);
        if (suffix2 != null && !suffix2.isEmpty()) {
            builder.append("SUFFIX_DELIMITER").append(suffix2);
        }
        return builder.toString();
    }

}
