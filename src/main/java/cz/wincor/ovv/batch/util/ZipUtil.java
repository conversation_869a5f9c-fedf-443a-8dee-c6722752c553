package cz.wincor.ovv.batch.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import org.apache.commons.io.FilenameUtils;

/**
 * Util class responsible for compressing and uncompressing zip archives
 *
 * <AUTHOR>
 *
 */
public class ZipUtil {

    /**
     * Method compress incoming files into a ZIP file
     * @param zipFile the absolute path to a ZIP file which will be created
     * @param filesToCompress one or more files to be compressed (absolute paths)
     * @throws FileNotFoundException
     * @throws IOException
     */
    public static void pack(String zipFile, String... filesToCompress) throws IOException {
        try (ZipOutputStream output = new ZipOutputStream(new FileOutputStream(zipFile))) {
            for (String file : filesToCompress) {
                ZipEntry e = new ZipEntry(FilenameUtils.getName(file));
                output.putNextEntry(e);
                byte[] data = Files.readAllBytes(Paths.get(file));
                output.write(data, 0, data.length);
                output.closeEntry();
            }
        }
    }

    /**
     * Uncompress an incoming ZIP file into a defined output directory
     *
     * @param zipFile the absolute path to a file
     *            input zip file
     * @param outputFolder
     *            zip file output folder
     * @throws IOException
     */
    public static List<String> unpack(String zipFile, String outputFolder) throws IOException {

        List<String> decompressedFiles = new ArrayList<>();

        byte[] buffer = new byte[1024];

        // create output directory is not exists
        File folder = new File(outputFolder);
        if (!folder.exists()) {
            folder.mkdir();
        }

        // get the zip file content
        ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFile));
        // get the zipped file list entry
        ZipEntry ze = zis.getNextEntry();

        while (ze != null) {

            String fileName = ze.getName();
            File newFile = new File(outputFolder, fileName);

            // create all non exists folders
            // else you will hit FileNotFoundException for compressed folder
            new File(newFile.getParent()).mkdirs();

            decompressedFiles.add(newFile.getAbsolutePath());
            FileOutputStream fos = new FileOutputStream(newFile);

            int len;
            while ((len = zis.read(buffer)) > 0) {
                fos.write(buffer, 0, len);
            }

            fos.close();
            ze = zis.getNextEntry();
        }
        zis.closeEntry();
        zis.close();
        return decompressedFiles;
    }

}
