package cz.wincor.ovv.batch.authentication.radius;

import java.util.Collections;
import java.util.List;

import jakarta.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;
import org.tinyradius.packet.RadiusPacket;
import org.tinyradius.util.RadiusClient;

/**
 * Component responsible for authentication against radius server
 * <AUTHOR>
 *
 */
@Component
public class RadiusAuthenticationProvider implements AuthenticationProvider {

    private static final String ROLE_ADMIN = "ROLE_ADMIN";

    private static final Logger logger = LoggerFactory.getLogger(RadiusAuthenticationProvider.class);

    @Value("${batch.authentication.radius.enabled}")
    private boolean enabled;

    @Value("${batch.authentication.radius.server}")
    private String serverConfigurationToken;

    @Value("${batch.authentication.radius.radiusid}")
    private String radiusId;

    private List<RadiusServer> servers;

    private Client client;

    private static final GrantedAuthority ADMIN_ROLE = new SimpleGrantedAuthority(ROLE_ADMIN);

    @PostConstruct
    public void initServers() {
        servers = RadiusUtil.parseServerConfigurationToken(serverConfigurationToken);
        client = new Client(radiusId);

    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String name = authentication.getName();
        String password = authentication.getCredentials().toString();

        if (enabled) {
            return authenticateInternally(name, password);
        } else {
            return null;
        }
    }

    private Authentication authenticateInternally(String username, String password) {
        RadiusPacket response = null;
        int attemptCount = 0;
        while (response == null && attemptCount++ < servers.size()) {
            RadiusServer radiusServer = servers.get(attemptCount - 1);
            logger.info("Calling radius server {} t authenticate user {}", radiusServer.getIp(), username);
            RadiusClient rc = new RadiusClient(radiusServer.getIp(), radiusServer.getSecret());
            try {
                //Set SO Timeout in milliseconds (originally in seconds)
                rc.setSocketTimeout(radiusServer.getTimeout() * 1000);
                response = client.callService(rc, username, password);
            } catch (Exception e) {
                logger.error("Exception when calling remote radius server.", e);
            }
        }

        if (response == null) {
            logger.warn("User {}, calling radius does not return any value.", username);
            return null;
        }

        if (response.getPacketType() == RadiusPacket.ACCESS_ACCEPT) {
            logger.info("User {} successfully authenticated using radius", username);
            return new UsernamePasswordAuthenticationToken(username, "", Collections.singletonList(ADMIN_ROLE));
        } else {
            logger.warn("User {}, returned response {}", username, response);
            return null;
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return authentication.equals(UsernamePasswordAuthenticationToken.class);
    }

}
