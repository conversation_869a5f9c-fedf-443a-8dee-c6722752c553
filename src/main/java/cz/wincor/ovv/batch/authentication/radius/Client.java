package cz.wincor.ovv.batch.authentication.radius;

import java.io.IOException;
import java.net.InetAddress;

import org.tinyradius.packet.AccessRequest;
import org.tinyradius.packet.RadiusPacket;
import org.tinyradius.util.RadiusClient;
import org.tinyradius.util.RadiusException;

/**
 * Class is responsible for communication with a remote radius server
 *
 * <AUTHOR>
 *
 */
public class Client {

    private static final String CALLING_STATION_ID = "Calling-Station-Id";
    private static final String NAS_IP_ADDRESS = "NAS-IP-Address";
    private static final String NAS_PORT_ID = "NAS-Port-Id";
    private String radiusId;

    public Client(String radiusId) {
        this.radiusId = radiusId;
    }

    public RadiusPacket callService(RadiusClient rc, String login, String heslo) throws IOException, RadiusException {
        AccessRequest ar = new AccessRequest(login, heslo);

        ar.setAuthProtocol(AccessRequest.AUTH_PAP);

        // Radius ID
        ar.addAttribute(NAS_PORT_ID, radiusId);

        // IP address server
        ar.addAttribute(NAS_IP_ADDRESS, InetAddress.getLocalHost().getHostAddress());

        // remote IP address
        ar.addAttribute(CALLING_STATION_ID, InetAddress.getLocalHost().getHostAddress());

        RadiusPacket response = rc.authenticate(ar);
        return response;
    }
}
