package cz.wincor.ovv.batch.authentication.radius;

import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

public class RadiusUtil {

    private RadiusUtil() {
        // TODO Auto-generated constructor stub
    }

    public static List<RadiusServer> parseServerConfigurationToken(String serverConfigurationToken)  {
        StringTokenizer tokenizer = new StringTokenizer(serverConfigurationToken, ";");

        List<RadiusServer> servers = new ArrayList<RadiusServer>(1 + tokenizer.countTokens() / 2);

        while (tokenizer.hasMoreTokens()) {
            String ip = tokenizer.nextToken();
            String secret;

            if (tokenizer.hasMoreElements())
                secret = tokenizer.nextToken();
            else
                break;

            if (tokenizer.hasMoreElements()) {
                RadiusServer ser = new RadiusServer(ip, secret);
                ser.setTimeout(Integer.parseInt(tokenizer.nextToken()));
                servers.add(ser);
            }
        }

        return servers;
    }

}
