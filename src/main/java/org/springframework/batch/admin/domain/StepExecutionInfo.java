/*
 * Copyright 2009-2015 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * 
 */
package org.springframework.batch.admin.domain;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.TimeZone;

import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;

public class StepExecutionInfo {

	private DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");

	private DateTimeFormatter timeFormat = DateTimeFormatter.ofPattern("HH:mm:ss");

	private Long id;

	private Long jobExecutionId;

	private String jobName;

	private String name;

	private String startDate = "-";

	private String startTime = "-";

	private String duration = "-";

	private StepExecution stepExecution;

	private long durationMillis;

	private String stepType = StepType.UNKNOWN.getDisplayName();

	private TimeZone timeZone = TimeZone.getTimeZone("UTC");

	public StepExecutionInfo(String jobName, Long jobExecutionId, String name, TimeZone timeZone) {
		this.jobName = jobName;
		this.jobExecutionId = jobExecutionId;
		this.name = name;
		this.stepExecution = new StepExecution(name, new JobExecution(jobExecutionId));

		if(timeZone != null) {
			this.timeZone = timeZone;
		}
	}

	public StepExecutionInfo(StepExecution stepExecution, TimeZone timeZone) {

		if(timeZone != null) {
			timeZone = this.timeZone;
		}

		this.stepExecution = stepExecution;
		this.id = stepExecution.getId();
		this.name = stepExecution.getStepName();
		this.jobName = stepExecution.getJobExecution() == null
				|| stepExecution.getJobExecution().getJobInstance() == null ? "?" : stepExecution.getJobExecution()
				.getJobInstance().getJobName();
		this.jobExecutionId = stepExecution.getJobExecutionId();
		if (stepExecution.getStartTime() != null) {
			this.startDate = stepExecution.getStartTime().atZone(timeZone.toZoneId()).format(dateFormat);
			this.startTime = stepExecution.getStartTime().atZone(timeZone.toZoneId()).format(timeFormat);

			LocalDateTime endTime = stepExecution.getEndTime() != null ? stepExecution.getEndTime() : LocalDateTime.now();
			Duration delta = Duration.ofMillis(ChronoUnit.MILLIS.between(stepExecution.getStartTime(), endTime));
			long hours = delta.toHours();
			long mins = delta.minusHours(hours).toMinutes();
			long secs = delta.minusHours(hours).minusMinutes(mins).getSeconds();
			this.duration = String.format("%02d:%02d:%02d", hours, mins, secs);
		}

	}

	public Long getId() {
		return id;
	}

	public Long getJobExecutionId() {
		return jobExecutionId;
	}

	public String getName() {
		return name;
	}

	public String getJobName() {
		return jobName;
	}

	public String getStartDate() {
		return startDate;
	}

	public String getStartTime() {
		return startTime;
	}

	public String getDuration() {
		return duration;
	}

	public long getDurationMillis() {
		return durationMillis;
	}
	
	public String getStatus() {
		if (id!=null) {
			return stepExecution.getStatus().toString();
		}
		return "NONE";
	}

	public String getExitCode() {
		if (id!=null) {
			return stepExecution.getExitStatus().getExitCode();
		}
		return "NONE";
	}

	public StepExecution getStepExecution() {
		return stepExecution;
	}

	public String getStepType() { return this.stepType; }

	public TimeZone getTimeZone() {
		return timeZone;
	}
}