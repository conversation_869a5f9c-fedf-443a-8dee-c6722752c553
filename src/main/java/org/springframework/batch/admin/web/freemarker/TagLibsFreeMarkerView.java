package org.springframework.batch.admin.web.freemarker;

import freemarker.ext.jakarta.jsp.TaglibFactory;
import freemarker.ext.jakarta.servlet.AllHttpScopesHashModel;
import freemarker.ext.jakarta.servlet.FreemarkerServlet;
import freemarker.ext.jakarta.servlet.HttpRequestHashModel;
import freemarker.ext.jakarta.servlet.HttpRequestParametersHashModel;
import freemarker.ext.jakarta.servlet.HttpSessionHashModel;
import freemarker.ext.jakarta.servlet.ServletContextHashModel;
import freemarker.template.SimpleHash;
import jakarta.servlet.GenericServlet;
import jakarta.servlet.ServletConfig;
import jakarta.servlet.ServletContext;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanInitializationException;

import java.util.Collections;
import java.util.Enumeration;
import java.util.Map;

// Can be removed once Spring 6.2 is released and just use AjaxFreeMarkerView
public class TagLibsFreeMarkerView extends AjaxFreeMarkerView {

    private TaglibFactory taglibFactory;
    private ServletContextHashModel servletContextHashModel;

    @Override
    protected void initServletContext(ServletContext servletContext) throws BeansException {
        super.initServletContext(servletContext);
        this.taglibFactory = new TaglibFactory(servletContext);
        if (this.taglibFactory.getObjectWrapper() == null) {
            this.taglibFactory.setObjectWrapper(super.getConfiguration().getObjectWrapper());
        }

        GenericServlet servlet = new GenericServletAdapter();
        try {
            servlet.init(new DelegatingServletConfig());
        } catch (ServletException ex) {
            throw new BeanInitializationException("Initialization of GenericServlet adapter failed", ex);
        }
        this.servletContextHashModel = new ServletContextHashModel(servlet, getObjectWrapper());
    }

    @Override
    protected SimpleHash buildTemplateModel(Map<String, Object> model, HttpServletRequest request, HttpServletResponse response) {
        AllHttpScopesHashModel fmModel = new AllHttpScopesHashModel(getObjectWrapper(), getServletContext(), request);
        fmModel.put(FreemarkerServlet.KEY_JSP_TAGLIBS, this.taglibFactory);
        fmModel.put(FreemarkerServlet.KEY_APPLICATION, this.servletContextHashModel);
        fmModel.put(FreemarkerServlet.KEY_SESSION, buildSessionModel(request, response));
        fmModel.put(FreemarkerServlet.KEY_REQUEST, new HttpRequestHashModel(request, response, getObjectWrapper()));
        fmModel.put(FreemarkerServlet.KEY_REQUEST_PARAMETERS, new HttpRequestParametersHashModel(request));
        fmModel.putAll(model);
        return fmModel;
    }

    /**
     * Build a FreeMarker {@link HttpSessionHashModel} for the given request,
     * detecting whether a session already exists and reacting accordingly.
     * @param request current HTTP request
     * @param response current servlet response
     * @return the FreeMarker HttpSessionHashModel
     */
    private HttpSessionHashModel buildSessionModel(HttpServletRequest request, HttpServletResponse response) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return new HttpSessionHashModel(session, getObjectWrapper());
        }
        else {
            return new HttpSessionHashModel(null, request, response, getObjectWrapper());
        }
    }


    /**
     * Simple adapter class that extends {@link GenericServlet}.
     * Needed for JSP access in FreeMarker.
     */
    private static class GenericServletAdapter extends GenericServlet {

        @Override
        public void service(ServletRequest servletRequest, ServletResponse servletResponse) {
            // no-op
        }
    }


    /**
     * Internal implementation of the {@link ServletConfig} interface,
     * to be passed to the servlet adapter.
     */
    private class DelegatingServletConfig implements ServletConfig {

        @Override
        public String getServletName() {
            return TagLibsFreeMarkerView.this.getBeanName();
        }

        @Override
        public ServletContext getServletContext() {
            return TagLibsFreeMarkerView.this.getServletContext();
        }

        @Override
        public String getInitParameter(String paramName) {
            return null;
        }

        @Override
        public Enumeration<String> getInitParameterNames() {
            return Collections.enumeration(Collections.emptySet());
        }
    }
}
