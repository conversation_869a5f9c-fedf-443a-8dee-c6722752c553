package org.springframework.batch.admin.web.util;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@Controller
public class HomeController {

    @RequestMapping(value = "/", method = RequestMethod.GET)
    public String home(RedirectAttributes redirectAttributes) {
        redirectAttributes.addAttribute("pageSize", "100");
        return "redirect:jobs";
    }
}
