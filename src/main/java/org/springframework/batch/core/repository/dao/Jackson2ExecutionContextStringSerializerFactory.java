package org.springframework.batch.core.repository.dao;

import org.springframework.beans.factory.FactoryBean;

import java.io.File;
import java.net.URL;
import java.util.HashSet;
import java.util.Set;

public class Jackson2ExecutionContextStringSerializerFactory implements FactoryBean<Jackson2ExecutionContextStringSerializer> {

    private final Set<String> OVV_JOB_CONTEXT_SERIALIZED_CLASSES = Set.of(
            "java.io.File" // used by recoReportBatchItemWriter
    );

    private String trustedBasePackage;

    @Override
    public Jackson2ExecutionContextStringSerializer getObject() throws Exception {
        Set<String> trustedClassNames = findAllClassesInPackage(trustedBasePackage);
        trustedClassNames.addAll(OVV_JOB_CONTEXT_SERIALIZED_CLASSES);
        return new Jackson2ExecutionContextStringSerializer(trustedClassNames.toArray(String[]::new));
    }

    @Override
    public Class<?> getObjectType() {
        return Jackson2ExecutionContextStringSerializer.class;
    }

    public void setTrustedBasePackage(String trustedBasePackage) {
        this.trustedBasePackage = trustedBasePackage;
    }

    private static Set<String> findAllClassesInPackage(String packageName) {
        Set<String> classes = new HashSet<>();
        String path = packageName.replace('.', '/');

        try {
            URL resource = Thread.currentThread().getContextClassLoader().getResource(path);
            if (resource != null) {
                File directory = new File(resource.toURI());
                if (directory.exists()) {
                    findClassesInDirectory(directory, packageName, classes);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return classes;
    }

    private static void findClassesInDirectory(File directory, String packageName, Set<String> classes) {
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    findClassesInDirectory(file, packageName + "." + file.getName(), classes);
                } else if (file.getName().endsWith(".class")) {
                    String className = packageName + '.' + file.getName().substring(0, file.getName().length() - 6);
                    classes.add(className);
                }
            }
        }
    }
}
