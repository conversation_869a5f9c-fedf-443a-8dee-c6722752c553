package org.springframework.batch.core.converter;

import org.springframework.batch.core.JobParameter;

public class StringOnlyJobParametersConverter extends DefaultJobParametersConverter {

    @Override
    protected String encode(JobParameter<?> jobParameter) {
        return jobParameter.getValue().toString();
    }

    @Override
    protected JobParameter<?> decode(String encodedJobParameter) {
        return new JobParameter<>(encodedJobParameter, String.class);
    }
}
