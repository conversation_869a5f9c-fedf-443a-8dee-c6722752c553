package org.springframework.batch.item.database;

import java.util.Map;

public interface CustomPagingQueryProvider extends PagingQueryProvider {

    // this method was removed in Spring Batch 5, but Spring Batch Admin relies on it
    String generateJumpToItemQuery(int itemIndex, int pageSize);

    // expected in bean factory, provided by AbstractSqlPagingQueryProvider
    void setFromClause(String fromClause);
    void setWhereClause(String whereClause);
    void setSortKeys(Map<String, Order> sortKeys);
    void setSelectClause(String selectClause);
    void setGroupClause(String groupClause);
}
