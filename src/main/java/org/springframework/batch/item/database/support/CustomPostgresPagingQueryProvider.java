package org.springframework.batch.item.database.support;

import org.springframework.batch.item.database.CustomPagingQueryProvider;

public class CustomPostgresPagingQueryProvider extends PostgresPagingQueryProvider implements CustomPagingQueryProvider {

    @Override
    public String generateJumpToItemQuery(int itemIndex, int pageSize) {
        int page = itemIndex / pageSize;
        int offset = (page * pageSize) - 1;
        offset = offset < 0 ? 0 : offset;
        String limitClause = new StringBuilder().append("LIMIT 1 OFFSET ").append(offset).toString();
        return CustomSqlPagingQueryUtils.generateLimitJumpToQuery(this, limitClause);
    }
}
