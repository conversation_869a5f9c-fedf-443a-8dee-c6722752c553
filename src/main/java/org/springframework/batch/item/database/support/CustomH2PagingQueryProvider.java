package org.springframework.batch.item.database.support;

import org.springframework.batch.item.database.CustomPagingQueryProvider;

public class CustomH2PagingQueryProvider extends H2PagingQueryProvider implements CustomPagingQueryProvider {

    @Override
    public String generateJumpToItemQuery(int itemIndex, int pageSize) {
        int page = itemIndex / pageSize;
        int offset = (page * pageSize) - 1;
        offset = offset < 0 ? 0 : offset;

        String topClause = new StringBuilder().append("LIMIT ").append(offset).append(" 1").toString();
        return CustomSqlPagingQueryUtils.generateTopJumpToQuery(this, topClause);
    }
}
