package org.springframework.batch.item.database.support;

import org.springframework.batch.item.database.Order;
import org.springframework.util.StringUtils;

import java.util.Map;

public class CustomSqlPagingQueryUtils extends SqlPagingQueryUtils {

    public static String generateRowNumSqlQueryWithNesting(AbstractSqlPagingQueryProvider provider,
                                                           String innerSelectClause, String outerSelectClause, boolean remainingPageQuery, String rowNumClause) {

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ").append(outerSelectClause).append(" FROM (SELECT ").append(outerSelectClause)
                .append(", ").append(StringUtils.hasText(provider.getGroupClause()) ? "MIN(ROWNUM) as TMP_ROW_NUM" : "ROWNUM as TMP_ROW_NUM");
        sql.append(" FROM (SELECT ").append(innerSelectClause).append(" FROM ").append(provider.getFromClause());
        buildWhereClause(provider, remainingPageQuery, sql);
        buildGroupByClause(provider, sql);
        sql.append(" ORDER BY ").append(buildSortClause(provider));
        sql.append(")) WHERE ").append(rowNumClause);

        return sql.toString();

    }

    /**
     * Generate SQL query string using a LIMIT clause
     *
     * @param provider {@link AbstractSqlPagingQueryProvider} providing the
     * implementation specifics
     * @param limitClause the implementation specific top clause to be used
     * @return the generated query
     */
    public static String generateLimitJumpToQuery(AbstractSqlPagingQueryProvider provider, String limitClause) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ").append(buildSortKeySelect(provider));
        sql.append(" FROM ").append(provider.getFromClause());
        sql.append(provider.getWhereClause() == null ? "" : " WHERE " + provider.getWhereClause());
        buildGroupByClause(provider, sql);
        sql.append(" ORDER BY ").append(buildSortClause(provider));
        sql.append(" " + limitClause);

        return sql.toString();
    }

    /**
     * Generate SQL query string using a TOP clause
     *
     * @param provider {@link AbstractSqlPagingQueryProvider} providing the
     * implementation specifics
     * @param topClause the implementation specific top clause to be used
     * @return the generated query
     */
    public static String generateTopJumpToQuery(AbstractSqlPagingQueryProvider provider, String topClause) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ").append(topClause).append(" ").append(buildSortKeySelect(provider));
        sql.append(" FROM ").append(provider.getFromClause());
        sql.append(provider.getWhereClause() == null ? "" : " WHERE " + provider.getWhereClause());
        buildGroupByClause(provider, sql);
        sql.append(" ORDER BY ").append(buildSortClause(provider));

        return sql.toString();
    }

    private static String buildSortKeySelect(AbstractSqlPagingQueryProvider provider) {
        StringBuilder select = new StringBuilder();

        String prefix = "";

        for (Map.Entry<String, Order> sortKey : provider.getSortKeysWithoutAliases().entrySet()) {
            select.append(prefix);

            prefix = ", ";

            select.append(sortKey.getKey());
        }

        return select.toString();
    }

    private static void buildWhereClause(AbstractSqlPagingQueryProvider provider, boolean remainingPageQuery,
                                         StringBuilder sql) {
        if (remainingPageQuery) {
            sql.append(" WHERE ");
            if (provider.getWhereClause() != null) {
                sql.append("(");
                sql.append(provider.getWhereClause());
                sql.append(") AND ");
            }

            buildSortConditions(provider, sql);
        } else {
            sql.append(provider.getWhereClause() == null ? "" : " WHERE " + provider.getWhereClause());
        }
    }

    private static void buildGroupByClause(AbstractSqlPagingQueryProvider provider, StringBuilder sql) {
        if (StringUtils.hasText(provider.getGroupClause())) {
            sql.append(" GROUP BY ");
            sql.append(provider.getGroupClause());
        }
    }
}
