package org.springframework.batch.item.database.support;

import org.springframework.batch.item.database.CustomPagingQueryProvider;
import org.springframework.batch.item.database.Order;
import org.springframework.batch.support.DatabaseType;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.jdbc.support.MetaDataAccessException;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import static org.springframework.batch.support.DatabaseType.H2;

import static org.springframework.batch.support.DatabaseType.ORACLE;
import static org.springframework.batch.support.DatabaseType.POSTGRES;

public class CustomSqlPagingQueryProviderFactoryBean implements FactoryBean<CustomPagingQueryProvider> {

    private DataSource dataSource;

    private String databaseType;

    private String fromClause;

    private String whereClause;

    private String selectClause;

    private String groupClause;

    private Map<String, Order> sortKeys;

    private final Map<DatabaseType, CustomPagingQueryProvider> providers = new HashMap<>();

    {
        providers.put(H2, new CustomH2PagingQueryProvider());
        providers.put(ORACLE, new CustomOraclePagingQueryProvider());
        providers.put(POSTGRES, new CustomPostgresPagingQueryProvider());
    }

    /**
     * @param groupClause SQL GROUP BY clause part of the SQL query string
     */
    public void setGroupClause(String groupClause) {
        this.groupClause = groupClause;
    }

    /**
     * @param databaseType the databaseType to set
     */
    public void setDatabaseType(String databaseType) {
        this.databaseType = databaseType;
    }

    /**
     * @param dataSource the dataSource to set
     */
    public void setDataSource(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    /**
     * @param fromClause the fromClause to set
     */
    public void setFromClause(String fromClause) {
        this.fromClause = fromClause;
    }

    /**
     * @param whereClause the whereClause to set
     */
    public void setWhereClause(String whereClause) {
        this.whereClause = whereClause;
    }

    /**
     * @param selectClause the selectClause to set
     */
    public void setSelectClause(String selectClause) {
        this.selectClause = selectClause;
    }

    /**
     * @param sortKeys the sortKeys to set
     */
    public void setSortKeys(Map<String, Order> sortKeys) {
        this.sortKeys = sortKeys;
    }

    public void setSortKey(String key) {
        Assert.doesNotContain(key, ",", "String setter is valid for a single ASC key only");

        Map<String, Order> keys = new LinkedHashMap<>();
        keys.put(key, Order.ASCENDING);

        this.sortKeys = keys;
    }

    /**
     * Get a {@link CustomPagingQueryProvider} instance using the provided properties and
     * appropriate for the given database type.
     *
     * @see FactoryBean#getObject()
     */
    @Override
    public CustomPagingQueryProvider getObject() throws Exception {

        DatabaseType type;
        try {
            type = databaseType != null ? DatabaseType.valueOf(databaseType.toUpperCase())
                    : DatabaseType.fromMetaData(dataSource);
        }
        catch (MetaDataAccessException e) {
            throw new IllegalArgumentException(
                    "Could not inspect meta data for database type.  You have to supply it explicitly.", e);
        }

        CustomPagingQueryProvider provider = providers.get(type);
        Assert.state(provider != null, "DatabaseType=" + type + " not implemented.");

        provider.setFromClause(fromClause);
        provider.setWhereClause(whereClause);
        provider.setSortKeys(sortKeys);
        if (StringUtils.hasText(selectClause)) {
            provider.setSelectClause(selectClause);
        }
        if (StringUtils.hasText(groupClause)) {
            provider.setGroupClause(groupClause);
        }

        provider.init(dataSource);

        return provider;

    }

    /**
     * Always returns {@link CustomPagingQueryProvider}.
     *
     * @see FactoryBean#getObjectType()
     */
    @Override
    public Class<CustomPagingQueryProvider> getObjectType() {
        return CustomPagingQueryProvider.class;
    }

    /**
     * Always returns true.
     * @see FactoryBean#isSingleton()
     */
    @Override
    public boolean isSingleton() {
        return true;
    }
}
