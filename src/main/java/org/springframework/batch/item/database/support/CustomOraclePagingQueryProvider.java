package org.springframework.batch.item.database.support;

import org.springframework.batch.item.database.CustomPagingQueryProvider;
import org.springframework.batch.item.database.Order;

import java.util.Map;

public class CustomOraclePagingQueryProvider extends OraclePagingQueryProvider implements CustomPagingQueryProvider {

    @Override
    public String generateJumpToItemQuery(int itemIndex, int pageSize) {
        int page = itemIndex / pageSize;
        int offset = (page * pageSize);
        offset = offset == 0 ? 1 : offset;
        String sortKeySelect = this.getSortKeySelect();
        return CustomSqlPagingQueryUtils.generateRowNumSqlQueryWithNesting(this, sortKeySelect, sortKeySelect, false, "TMP_ROW_NUM = "
                + offset);
    }

    private String getSortKeySelect() {
        StringBuilder sql = new StringBuilder();
        String prefix = "";

        for (Map.Entry<String, Order> sortKey : this.getSortKeys().entrySet()) {
            sql.append(prefix);
            prefix = ", ";
            sql.append(sortKey.getKey());
        }

        return sql.toString();
    }
}
