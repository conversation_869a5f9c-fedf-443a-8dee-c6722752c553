# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

OVV Batch is a Spring Batch web application for processing voucher transactions across multiple countries (CZ, HU, PL, SK) and providers (Edenred, Sodexo, TVM, LCHD, WASA, DOXX). It handles batch processing, file generation, compression, and SFTP uploads.

- **Build System**: Maven
- **Java Version**: 17
- **Framework**: Spring Framework 6.1.14 with Spring Batch 5.1.2
- **Packaging**: WAR file (ovv-batch.war)
- **Web Container**: Tomcat 10 (production), Jetty (development)

## Common Development Commands

### Build and Test
```bash
mvn clean package              # Build the WAR file
mvn test                       # Run all tests
mvn test -Dtest=TestClassName  # Run specific test
mvn jetty:run                  # Run locally at http://localhost:8080/ovv-batch
mvn dependency-check:check     # Security vulnerability scan
```

### Docker Development Environment
```bash
cd tools
docker-compose up -d           # Start Oracle DB and FreeRADIUS
docker-compose down            # Stop services
```

### Debugging
- Remote debugging available on port 5005
- Debug with: `-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005`

## High-Level Architecture

### Package Structure
- **Controllers** (`cz.wincor.ovv.batch.controller`): Web layer, handles authentication UI
- **Services** (`cz.wincor.ovv.batch.service`): Business logic layer with transactional operations
- **Repositories** (`cz.wincor.ovv.batch.repository`): Data access layer using Spring Data JPA
- **Entities** (`cz.wincor.ovv.batch.entity`): JPA entities for batch and OVV domains
- **Batch Components**:
  - **Tasklets** (`cz.wincor.ovv.batch.tasklet`): File operations, SFTP, compression
  - **Readers/Writers**: Custom implementations for batch processing
  - **Schedulers**: Daily and weekly job execution
- **Spring Batch Admin Extension**: Custom UI and management features

### Batch Job Pipeline
1. Setup output file (OvvOutputFileGenerator)
2. Set batch interval (OvvBatchIntervalGenerator)
3. Load OVV stores (LoadOvvStoreTasklet)
4. Read and process data (custom readers/writers)
5. Add headers/footers (BatchFileHeaderTasklet)
6. Compress files (BatchFileCompressTasklet)
7. Upload to SFTP (SftpUploadTasklet)

### Key Technologies
- **Database**: Oracle (production), H2 (testing)
- **Authentication**: RADIUS via TinyRadius
- **File Transfer**: SFTP using Spring Integration
- **Job Monitoring**: Spring Batch Admin web interface
- **Templates**: FreeMarker for web UI

### Configuration Locations
- Batch job definitions: `src/main/resources/META-INF/spring/batch/jobs/`
- Database scripts: `db/sql/` (schemas, migrations)
- Spring configuration: `src/main/resources/META-INF/spring/`
- Test resources: `src/test/resources/`