-- Spring Batch 4.1
ALTER TABLE BATCH_JOB_INSTANCE MODIFY JOB_NAME VARCHAR2(100 char);
ALTER TABLE BATCH_JOB_INSTANCE MODIFY JOB_KEY VARCHAR2(32 char);

ALTER TABLE BATCH_JOB_EXECUTION MODIFY STATUS VARCHAR2(10 char);
ALTER TABLE BATCH_JOB_EXECUTION MODIFY EXIT_CODE VARCHAR2(2500 char);
ALTER TABLE BATCH_JOB_EXECUTION MODIFY EXIT_MESSAGE VARCHAR2(2500 char);
ALTER TABLE BATCH_JOB_EXECUTION MODIFY JOB_CONFIGURATION_LOCATION VARCHAR2(2500 char);

ALTER TABLE BATCH_JOB_EXECUTION_PARAMS MODIFY TYPE_CD VARCHAR2(6 char);
ALTER TABLE BATCH_JOB_EXECUTION_PARAMS MODIFY KEY_NAME VARCHAR2(100 char);
ALTER TABLE BATCH_JOB_EXECUTION_PARAMS MODIFY STRING_VAL VARCHAR2(250 char);

ALTER TABLE BATCH_STEP_EXECUTION MODIFY STEP_NAME VARCHAR2(100 char);
ALTER TABLE BATCH_STEP_EXECUTION MODIFY STATUS VARCHAR2(10 char);
ALTER TABLE BATCH_STEP_EXECUTION MODIFY EXIT_CODE VARCHAR2(2500 char);
ALTER TABLE BATCH_STEP_EXECUTION MODIFY EXIT_MESSAGE VARCHAR2(2500 char);

ALTER TABLE BATCH_STEP_EXECUTION_CONTEXT MODIFY SHORT_CONTEXT VARCHAR2(2500 char);
ALTER TABLE BATCH_JOB_EXECUTION_CONTEXT MODIFY SHORT_CONTEXT VARCHAR2(2500 char);

-- Spring Batch 5.0
ALTER SEQUENCE BATCH_STEP_EXECUTION_SEQ ORDER;
ALTER SEQUENCE BATCH_JOB_EXECUTION_SEQ ORDER;
ALTER SEQUENCE BATCH_JOB_SEQ ORDER;

ALTER TABLE BATCH_STEP_EXECUTION ADD CREATE_TIME TIMESTAMP DEFAULT TO_TIMESTAMP('1970-01-01 00:00:00', 'yyyy-MM-dd HH24:mi:ss') NOT NULL;
ALTER TABLE BATCH_STEP_EXECUTION MODIFY START_TIME TIMESTAMP NULL;

ALTER TABLE BATCH_JOB_EXECUTION_PARAMS MODIFY TYPE_CD VARCHAR(100);
ALTER TABLE BATCH_JOB_EXECUTION_PARAMS RENAME COLUMN TYPE_CD TO PARAMETER_TYPE;
UPDATE BATCH_JOB_EXECUTION_PARAMS
SET PARAMETER_TYPE = CASE
    WHEN PARAMETER_TYPE = 'STRING' THEN 'java.lang.String'
    WHEN PARAMETER_TYPE = 'DATE' THEN 'java.util.Date'
    WHEN PARAMETER_TYPE = 'LONG' THEN 'java.lang.Long'
    WHEN PARAMETER_TYPE = 'DOUBLE' THEN 'java.lang.Double'
    ELSE PARAMETER_TYPE -- Retain original value if it doesn't match
END;

ALTER TABLE BATCH_JOB_EXECUTION_PARAMS MODIFY KEY_NAME VARCHAR(100);
ALTER TABLE BATCH_JOB_EXECUTION_PARAMS RENAME COLUMN KEY_NAME TO PARAMETER_NAME;

ALTER TABLE BATCH_JOB_EXECUTION_PARAMS ADD PARAMETER_VALUE VARCHAR2(2500);
UPDATE BATCH_JOB_EXECUTION_PARAMS
SET PARAMETER_VALUE = CASE
    WHEN PARAMETER_TYPE = 'java.lang.String' THEN STRING_VAL
    WHEN PARAMETER_TYPE = 'java.util.Date' THEN TO_CHAR(DATE_VAL, 'YYYY-MM-DD"T"HH24:MI:SS.FF3"Z"') -- Convert date to the specified format
    WHEN PARAMETER_TYPE = 'java.lang.Long' THEN TO_CHAR(LONG_VAL)
    WHEN PARAMETER_TYPE = 'java.lang.Double' THEN TO_CHAR(DOUBLE_VAL)
    ELSE NULL -- Handle cases where TYPE_CD does not match any known value
END;

ALTER TABLE BATCH_JOB_EXECUTION_PARAMS DROP COLUMN STRING_VAL;
ALTER TABLE BATCH_JOB_EXECUTION_PARAMS DROP COLUMN DATE_VAL;
ALTER TABLE BATCH_JOB_EXECUTION_PARAMS DROP COLUMN LONG_VAL;
ALTER TABLE BATCH_JOB_EXECUTION_PARAMS DROP COLUMN DOUBLE_VAL;

ALTER TABLE BATCH_JOB_EXECUTION MODIFY CREATE_TIME TIMESTAMP(9);
ALTER TABLE BATCH_JOB_EXECUTION MODIFY START_TIME TIMESTAMP(9);
ALTER TABLE BATCH_JOB_EXECUTION MODIFY END_TIME TIMESTAMP(9);
ALTER TABLE BATCH_JOB_EXECUTION MODIFY LAST_UPDATED TIMESTAMP(9);

ALTER TABLE BATCH_STEP_EXECUTION MODIFY CREATE_TIME TIMESTAMP(9);
ALTER TABLE BATCH_STEP_EXECUTION MODIFY START_TIME TIMESTAMP(9);
ALTER TABLE BATCH_STEP_EXECUTION MODIFY END_TIME TIMESTAMP(9);
ALTER TABLE BATCH_STEP_EXECUTION MODIFY LAST_UPDATED TIMESTAMP(9);
