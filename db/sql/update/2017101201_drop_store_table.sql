ALTER TABLE report_batch DROP CONSTRAINT FKA2QI6C9S9049H8ML5JNYFR33M;

-- Create database link - pouze pro účely update n<PERSON><PERSON><PERSON>
create database link OVV
  connect to OCVV
  using 'DEVSYNC1';
  
  
  
-- <PERSON><PERSON> se zmenit report_batch.store_id se stavajici tabulky store.id na ovv_store.id (abychom mohli tabulku store smazat)   
UPDATE report_batch set store_id = (select ovvs.id from store s left join ovv_store@ovv ovvs on (s.site_code = ovvs.site_code) where store_id = s.id)


DROP table store;
DROP sequence store_seq;