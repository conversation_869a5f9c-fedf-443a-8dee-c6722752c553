ALTER TABLE report_batch DROP CONSTRAINT FKA2QI6C9S9049H8ML5JNYFR33M;

-- Create database link - pouze pro účely update n<PERSON><PERSON><PERSON>
create database link OVV
  connect to OCVV
  using 'DEVSYNC1';

--Vybere report_file.id kde se má začít, pouze v případě, že by update celé tabulky byl pří<PERSON> n<PERSON>ný 
SELECT MIN(rf.id) FROM report_file rf where rf.created > TO_TIMESTAMP('2018-01-01 00:00:00.000', 'YYYY-MM-DD HH24:MI:SS.FF')   

--Vybere report_file.id kde se má skončit 
SELECT MAX(rf.id) FROM report_file rf where rf.created < TO_TIMESTAMP('2018-03-21 00:00:00.000', 'YYYY-MM-DD HH24:MI:SS.FF')     
  
-- Mu<PERSON> se zmenit report_batch.store_id se stavajici tabulky store.id na ovv_store.id (abychom mohli tabulku store smazat)   
UPDATE report_batch set store_id = (select ovvs.id from store s left join ovv_store@ovv ovvs on (s.site_code = ovvs.site_code) where store_id = s.id) where report_file_id >= MIN AND report_file_id <= MAX;   


DROP table store;
DROP sequence store_seq;