CREATE TABLE batch_user (
  id number(19,0) NOT NULL,
  password VARCHAR2(64 CHAR) NOT NULL,
  username VARCHAR2(64 CHAR) NOT NULL,
  enabled NUMBER(1,0) NOT NULL,
  PRIMARY KEY (id)
);

CREATE UNIQUE INDEX uix_batch_user_username ON batch_user (username);

CREATE TABLE user_roles (
  id number(19,0) NOT NULL,
  username VARCHAR2(64 CHAR) NOT NULL,
  role VARCHAR(45) NOT NULL,
  PRIMARY KEY (id)
);

CREATE UNIQUE INDEX uix_user_roles_user_role ON user_roles (username, role);

--Default password is 123456
INSERT INTO batch_user(id, username, password, enabled)
VALUES (1, 'admin','$2a$10$EblZqNptyYvcLm/VwDCVAuBjzZOI7khzdyGPBr08PpIi0na624b8.', 1);

INSERT INTO user_roles (id, username, role)
VALUES (1, 'admin', 'ROLE_ADMIN');
