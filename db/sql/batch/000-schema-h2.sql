
    create sequence report_batch_seq start with 1 increment by 1;

    create sequence report_file_seq start with 1 increment by 1;

    create table REPORT_BATCH (
        id bigint not null,
        ovv_transaction_request_id bigint,
        result_code integer,
        result_description varchar(255),
        voucher_number varchar(255),
        report_file_id bigint not null,
        store_id bigint not null,
        primary key (id)
    );

    create table REPORT_FILE (
        id bigint not null,
        country_code varchar(2) not null,
        created timestamp not null,
        file_name varchar(255) not null,
        filename_date varchar(255) not null,
        file_path varchar(255) not null,
        items_count integer not null,
        job_execution_id bigint not null,
        ovv_issuer varchar(255) not null,
        reco_job_execution_id bigint,
        reconciliated timestamp,
        uuid bigint not null,
        primary key (id)
    );

    create index IDX4f6g49tfeg5eiihqedjf8qem4 on REPORT_BATCH (report_file_id);

    create index IDXdt7ofrxlihekyg36154ukrmxa on REPORT_BATCH (voucher_number);

    alter table REPORT_BATCH 
        add constraint FK2d95p6rgo8jqxifb1e4w0cktx 
        foreign key (report_file_id) 
        references REPORT_FILE;

    alter table REPORT_FILE 
        add constraint UK8ln521fbbeb8lebole9e8ljq9 unique (ovv_issuer, uuid);
