
create sequence report_batch_seq start with 1 increment by 1;

create sequence report_file_seq start with 1 increment by 1;

create table REPORT_BATCH (
        id number(19,0) not null,
        ovv_transaction_request_id number(19,0),
        result_code number(10,0),
        result_description varchar2(255 char),
        voucher_number varchar2(255 char),
        report_file_id number(19,0) not null,
        store_id number(19,0) not null,
        primary key (id)
);

create table REPORT_FILE (
        id number(19,0) not null,
        country_code varchar2(2 char) not null,
        created timestamp not null,
        file_name varchar2(255 char) not null,
        filename_date varchar2(255 char) not null,
        file_path varchar2(255 char) not null,
        items_count number(10,0) not null,
        job_execution_id number(19,0) not null,
        ovv_issuer varchar2(255 char) not null,
        reco_job_execution_id number(19,0),
        reconciliated timestamp,
        uuid number(19,0) not null,
        primary key (id)
);
    
CREATE TABLE batch_user (
  id number(19,0) NOT NULL,
  password VARCHAR2(64 CHAR) NOT NULL,
  username VARCHAR2(64 CHAR) NOT NULL,
  enabled NUMBER(1,0) NOT NULL,
  PRIMARY KEY (id)
);



CREATE TABLE user_roles (
  id number(19,0) NOT NULL,
  username VARCHAR2(64 CHAR) NOT NULL,
  role VARCHAR(45) NOT NULL,
  PRIMARY KEY (id)
);

CREATE UNIQUE INDEX uix_batch_user_username ON batch_user (username);

CREATE UNIQUE INDEX uix_user_roles_user_role ON user_roles (username, role);

create index IDX4f6g49tfeg5eiihqedjf8qem4 on REPORT_BATCH (report_file_id);

create index IDXdt7ofrxlihekyg36154ukrmxa on REPORT_BATCH (voucher_number);

alter table REPORT_BATCH 
        add constraint FK2d95p6rgo8jqxifb1e4w0cktx 
        foreign key (report_file_id) 
        references REPORT_FILE;

alter table REPORT_FILE 
        add constraint UK8ln521fbbeb8lebole9e8ljq9 unique (ovv_issuer, uuid);
